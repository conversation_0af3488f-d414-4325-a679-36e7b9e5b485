# Windows Server Management v2.0 - Finale Version

Eine einfache, zuverlässige Windows Server Verwaltungsanwendung mit PowerShell.

## ✅ Finalisiertes Projekt

Das Projekt wurde erfolgreich finalisiert und alle unnötigen Dateien wurden entfernt. Es gibt jetzt nur noch die notwendigen Dateien für eine zuverlässige Ausführung.

## 📁 Projektdateien

```
├── ServerManagement.ps1          # Hauptanwendung (PowerShell)
├── Start-ServerManagement.cmd    # Einfacher Starter (.cmd)
└── README.md                     # Diese Dokumentation
```

## 🚀 Anwendung starten

### Option 1: Über CMD-Datei (Empfohlen)
Doppelklick auf `Start-ServerManagement.cmd`

### Option 2: Direkt über PowerShell
```powershell
powershell -ExecutionPolicy Bypass -File "ServerManagement.ps1"
```

### Option 3: In PowerShell-Konsole
```powershell
.\ServerManagement.ps1
```

## ✨ Features

- **🖥️ Interaktives Menü**: Einfache Navigation mit Zahlen
- **📊 Server-Übersicht**: Alle Server mit Status auf einen Blick
- **🔍 Ping-Funktionalität**: Einzelne Server oder alle gleichzeitig testen
- **🔄 Server-Neustart**: Einzeln oder alle in korrekter Reihenfolge
- **📝 Aktivitätsprotokoll**: Alle Aktionen werden protokolliert
- **🎨 Farbkodierte Ausgabe**: Übersichtliche Darstellung
- **⚡ Zuverlässig**: Läuft ohne externe Abhängigkeiten

## 🖥️ Standard-Server

Die Anwendung verwaltet folgende Server in der korrekten Neustart-Reihenfolge:

1. **DC01** - Domain Controller
2. **DC02** - Domain Controller  
3. **DB01** - Database Server
4. **APP01** - Application Server
5. **MAIL** - Mail Server
6. **FILE** - File Server
7. **TS01** - Terminal Server

## 📋 Menü-Optionen

1. **Server-Übersicht anzeigen** - Zeigt alle Server mit aktuellem Status
2. **Alle Server pingen** - Testet Konnektivität zu allen Servern
3. **Einzelnen Server pingen** - Wähle einen Server zum Testen
4. **Einzelnen Server neu starten** - Starte einen ausgewählten Server neu
5. **Alle Server neu starten** - Startet alle Server in korrekter Reihenfolge neu
6. **Aktivitätsprotokoll anzeigen** - Zeigt alle protokollierten Aktionen
7. **Einstellungen** - (In zukünftiger Version verfügbar)
8. **Über diese Anwendung** - Informationen zur Software
0. **Beenden** - Schließt die Anwendung

## 🔧 Technische Details

- **Sprache**: PowerShell 5.1+
- **Kompatibilität**: Windows 10/11, Windows Server 2016+
- **Abhängigkeiten**: Keine (nur Standard-PowerShell)
- **Berechtigungen**: Läuft ohne Administratorrechte
- **Größe**: Sehr klein (~15 KB)

## 🛡️ Sicherheitsfeatures

- **Bestätigungsdialoge** für kritische Aktionen
- **Sichere Neustart-Reihenfolge** (Domain Controller zuerst)
- **Keine Passwort-Speicherung**
- **Ausführung ohne Admin-Rechte**

## 📊 Status-Anzeige

- **🟢 Online (XXms)** - Server ist erreichbar mit Antwortzeit
- **🔴 Offline** - Server ist nicht erreichbar
- **🟡 Fehler** - Fehler beim Testen der Konnektivität
- **⚪ Unbekannt** - Status wurde noch nicht geprüft

## 🔮 Zukünftige Erweiterungen

- Konfigurierbare Server-Liste
- Windows Update Management
- Automatische Überwachung
- Export von Protokollen
- Anmeldedaten-Verwaltung

## 👨‍💻 Entwickler

**Tonino Gerns**  
*Windows Server Management v2.0*

---

## 🎯 Projektabschluss

✅ **Projekt erfolgreich finalisiert!**

- Alle unnötigen Dateien entfernt
- Zuverlässige PowerShell-Lösung implementiert
- Einfache Bedienung ohne komplexe Abhängigkeiten
- Moderne, übersichtliche Benutzeroberfläche
- Vollständige Dokumentation

Die Anwendung ist jetzt bereit für den produktiven Einsatz!
