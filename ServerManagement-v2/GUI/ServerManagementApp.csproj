<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <UseWPF>true</UseWPF>
    <AssemblyTitle>Windows Server Management v2.0</AssemblyTitle>
    <AssemblyDescription>Moderne Windows Server Verwaltung</AssemblyDescription>
    <AssemblyCompany>Tonino Gerns</AssemblyCompany>
    <AssemblyProduct>Server Management</AssemblyProduct>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>

    <!-- Single File Deployment Configuration -->
    <PublishSingleFile>true</PublishSingleFile>
    <SelfContained>true</SelfContained>
    <RuntimeIdentifier>win-x64</RuntimeIdentifier>
    <IncludeNativeLibrariesForSelfExtract>true</IncludeNativeLibrariesForSelfExtract>
    <EnableCompressionInSingleFile>true</EnableCompressionInSingleFile>



    <!-- Trim unused code -->
    <PublishTrimmed>false</PublishTrimmed>
    <TrimMode>partial</TrimMode>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="MaterialDesignThemes" Version="5.0.0" />
    <PackageReference Include="MaterialDesignColors" Version="3.1.0" />
    <PackageReference Include="System.Management.Automation" Version="7.4.0" />
  </ItemGroup>

  <!-- Copy PowerShell modules and config to output -->
  <ItemGroup>
    <None Include="../Modules/**/*" CopyToOutputDirectory="PreserveNewest" />
    <None Include="../Config/**/*" CopyToOutputDirectory="PreserveNewest" />
  </ItemGroup>

</Project>
