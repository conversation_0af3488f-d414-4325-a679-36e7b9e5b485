{"version": 3, "targets": {"net8.0-windows7.0": {"MaterialDesignColors/3.1.0": {"type": "package", "compile": {"lib/net8.0/MaterialDesignColors.dll": {"related": ".pdb"}}, "runtime": {"lib/net8.0/MaterialDesignColors.dll": {"related": ".pdb"}}}, "MaterialDesignThemes/5.0.0": {"type": "package", "dependencies": {"MaterialDesignColors": "3.0.0", "Microsoft.Xaml.Behaviors.Wpf": "1.1.39"}, "compile": {"lib/net7.0/MaterialDesignThemes.Wpf.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net7.0/MaterialDesignThemes.Wpf.dll": {"related": ".pdb;.xml"}}, "build": {"build/MaterialDesignThemes.targets": {}}}, "Microsoft.ApplicationInsights/2.21.0": {"type": "package", "dependencies": {"System.Diagnostics.DiagnosticSource": "5.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.ApplicationInsights.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.ApplicationInsights.dll": {"related": ".pdb;.xml"}}}, "Microsoft.Management.Infrastructure/3.0.0": {"type": "package", "dependencies": {"Microsoft.Management.Infrastructure.Runtime.Unix": "3.0.0", "Microsoft.Management.Infrastructure.Runtime.Win": "3.0.0"}, "compile": {"ref/netstandard1.6/Microsoft.Management.Infrastructure.Native.dll": {}, "ref/netstandard1.6/Microsoft.Management.Infrastructure.dll": {}}}, "Microsoft.Management.Infrastructure.Runtime.Unix/3.0.0": {"type": "package", "runtimeTargets": {"runtimes/unix/lib/netstandard1.6/Microsoft.Management.Infrastructure.dll": {"assetType": "runtime", "rid": "unix"}}}, "Microsoft.Management.Infrastructure.Runtime.Win/3.0.0": {"type": "package", "runtimeTargets": {"runtimes/win-arm64/lib/netstandard1.6/microsoft.management.infrastructure.dll": {"assetType": "runtime", "rid": "win-arm64"}, "runtimes/win-arm64/lib/netstandard1.6/microsoft.management.infrastructure.native.dll": {"assetType": "runtime", "rid": "win-arm64"}, "runtimes/win-arm64/native/microsoft.management.infrastructure.native.unmanaged.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/lib/netstandard1.6/microsoft.management.infrastructure.dll": {"assetType": "runtime", "rid": "win-x64"}, "runtimes/win-x64/lib/netstandard1.6/microsoft.management.infrastructure.native.dll": {"assetType": "runtime", "rid": "win-x64"}, "runtimes/win-x64/native/microsoft.management.infrastructure.native.unmanaged.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/lib/netstandard1.6/microsoft.management.infrastructure.dll": {"assetType": "runtime", "rid": "win-x86"}, "runtimes/win-x86/lib/netstandard1.6/microsoft.management.infrastructure.native.dll": {"assetType": "runtime", "rid": "win-x86"}, "runtimes/win-x86/native/microsoft.management.infrastructure.native.unmanaged.dll": {"assetType": "native", "rid": "win-x86"}}}, "Microsoft.NET.ILLink.Tasks/8.0.17": {"type": "package", "build": {"build/Microsoft.NET.ILLink.Tasks.props": {}}}, "Microsoft.PowerShell.CoreCLR.Eventing/7.4.0": {"type": "package", "dependencies": {"System.Diagnostics.EventLog": "8.0.0"}, "compile": {"ref/net8.0/Microsoft.PowerShell.CoreCLR.Eventing.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/Microsoft.PowerShell.CoreCLR.Eventing.dll": {"assetType": "runtime", "rid": "win"}}}, "Microsoft.PowerShell.Native/7.4.0": {"type": "package", "runtimeTargets": {"runtimes/linux-arm/native/libpsl-native.so": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/libpsl-native.so": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-musl-x64/native/libpsl-native.so": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/libpsl-native.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/osx/native/libpsl-native.dylib": {"assetType": "native", "rid": "osx"}, "runtimes/win-arm/native/PowerShell.Core.Instrumentation.dll": {"assetType": "native", "rid": "win-arm"}, "runtimes/win-arm/native/pwrshplugin.dll": {"assetType": "native", "rid": "win-arm"}, "runtimes/win-arm64/native/PowerShell.Core.Instrumentation.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-arm64/native/pwrshplugin.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/PowerShell.Core.Instrumentation.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x64/native/pwrshplugin.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/PowerShell.Core.Instrumentation.dll": {"assetType": "native", "rid": "win-x86"}, "runtimes/win-x86/native/pwrshplugin.dll": {"assetType": "native", "rid": "win-x86"}}}, "Microsoft.Security.Extensions/1.2.0": {"type": "package", "compile": {"ref/netstandard2.0/getfilesiginforedistwrapper.dll": {}}, "runtimeTargets": {"runtimes/win-arm/lib/net5.0/getfilesiginforedistwrapper.dll": {"assetType": "runtime", "rid": "win-arm"}, "runtimes/win-arm/native/getfilesiginforedist.dll": {"assetType": "native", "rid": "win-arm"}, "runtimes/win-arm64/lib/net5.0/getfilesiginforedistwrapper.dll": {"assetType": "runtime", "rid": "win-arm64"}, "runtimes/win-arm64/native/getfilesiginforedist.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/lib/net5.0/getfilesiginforedistwrapper.dll": {"assetType": "runtime", "rid": "win-x64"}, "runtimes/win-x64/native/getfilesiginforedist.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/lib/net5.0/getfilesiginforedistwrapper.dll": {"assetType": "runtime", "rid": "win-x86"}, "runtimes/win-x86/native/getfilesiginforedist.dll": {"assetType": "native", "rid": "win-x86"}}}, "Microsoft.Win32.Registry.AccessControl/8.0.0": {"type": "package", "compile": {"lib/net8.0/Microsoft.Win32.Registry.AccessControl.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Win32.Registry.AccessControl.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net8.0/Microsoft.Win32.Registry.AccessControl.dll": {"assetType": "runtime", "rid": "win"}}}, "Microsoft.Xaml.Behaviors.Wpf/1.1.39": {"type": "package", "compile": {"lib/net5.0-windows7.0/Microsoft.Xaml.Behaviors.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net5.0-windows7.0/Microsoft.Xaml.Behaviors.dll": {"related": ".pdb;.xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "Newtonsoft.Json/13.0.3": {"type": "package", "compile": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}}, "System.CodeDom/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.CodeDom.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.CodeDom.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Configuration.ConfigurationManager/8.0.0": {"type": "package", "dependencies": {"System.Diagnostics.EventLog": "8.0.0", "System.Security.Cryptography.ProtectedData": "8.0.0"}, "compile": {"lib/net8.0/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Diagnostics.DiagnosticSource/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Diagnostics.EventLog/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.Diagnostics.EventLog.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Diagnostics.EventLog.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Diagnostics.EventLog.Messages.dll": {"assetType": "runtime", "rid": "win"}, "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.dll": {"assetType": "runtime", "rid": "win"}}}, "System.DirectoryServices/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.DirectoryServices.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.DirectoryServices.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.DirectoryServices.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Formats.Asn1/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.Formats.Asn1.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Formats.Asn1.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Management/8.0.0": {"type": "package", "dependencies": {"System.CodeDom": "8.0.0"}, "compile": {"lib/net8.0/System.Management.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Management.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Management.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Management.Automation/7.4.0": {"type": "package", "dependencies": {"Microsoft.ApplicationInsights": "2.21.0", "Microsoft.Management.Infrastructure": "3.0.0", "Microsoft.PowerShell.CoreCLR.Eventing": "7.4.0", "Microsoft.PowerShell.Native": "7.4.0", "Microsoft.Security.Extensions": "1.2.0", "Microsoft.Win32.Registry.AccessControl": "8.0.0", "Newtonsoft.Json": "13.0.3", "System.Configuration.ConfigurationManager": "8.0.0", "System.Diagnostics.DiagnosticSource": "8.0.0", "System.DirectoryServices": "8.0.0", "System.Management": "8.0.0", "System.Security.AccessControl": "6.0.2-mauipre.1.22102.15", "System.Security.Cryptography.Pkcs": "8.0.0", "System.Security.Permissions": "8.0.0", "System.Text.Encoding.CodePages": "8.0.0"}, "compile": {"ref/net8.0/System.Management.Automation.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/net8.0/System.Management.Automation.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/net8.0/System.Management.Automation.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.AccessControl/6.0.2-mauipre.1.22102.15": {"type": "package", "compile": {"lib/net6.0/System.Security.AccessControl.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Security.AccessControl.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Security.AccessControl.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.Pkcs/8.0.0": {"type": "package", "dependencies": {"System.Formats.Asn1": "8.0.0"}, "compile": {"lib/net8.0/System.Security.Cryptography.Pkcs.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Security.Cryptography.Pkcs.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Security.Cryptography.Pkcs.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.ProtectedData/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.Security.Cryptography.ProtectedData.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Security.Cryptography.ProtectedData.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Security.Permissions/8.0.0": {"type": "package", "dependencies": {"System.Windows.Extensions": "8.0.0"}, "compile": {"lib/net8.0/System.Security.Permissions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Security.Permissions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Text.Encoding.CodePages/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.Text.Encoding.CodePages.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Text.Encoding.CodePages.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Text.Encoding.CodePages.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Windows.Extensions/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.Windows.Extensions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Windows.Extensions.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Windows.Extensions.dll": {"assetType": "runtime", "rid": "win"}}}}, "net8.0-windows7.0/win-x64": {"MaterialDesignColors/3.1.0": {"type": "package", "compile": {"lib/net8.0/MaterialDesignColors.dll": {"related": ".pdb"}}, "runtime": {"lib/net8.0/MaterialDesignColors.dll": {"related": ".pdb"}}}, "MaterialDesignThemes/5.0.0": {"type": "package", "dependencies": {"MaterialDesignColors": "3.0.0", "Microsoft.Xaml.Behaviors.Wpf": "1.1.39"}, "compile": {"lib/net7.0/MaterialDesignThemes.Wpf.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net7.0/MaterialDesignThemes.Wpf.dll": {"related": ".pdb;.xml"}}, "build": {"build/MaterialDesignThemes.targets": {}}}, "Microsoft.ApplicationInsights/2.21.0": {"type": "package", "dependencies": {"System.Diagnostics.DiagnosticSource": "5.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.ApplicationInsights.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.ApplicationInsights.dll": {"related": ".pdb;.xml"}}}, "Microsoft.Management.Infrastructure/3.0.0": {"type": "package", "dependencies": {"Microsoft.Management.Infrastructure.Runtime.Unix": "3.0.0", "Microsoft.Management.Infrastructure.Runtime.Win": "3.0.0"}, "compile": {"ref/netstandard1.6/Microsoft.Management.Infrastructure.Native.dll": {}, "ref/netstandard1.6/Microsoft.Management.Infrastructure.dll": {}}}, "Microsoft.Management.Infrastructure.Runtime.Unix/3.0.0": {"type": "package"}, "Microsoft.Management.Infrastructure.Runtime.Win/3.0.0": {"type": "package", "runtime": {"runtimes/win-x64/lib/netstandard1.6/microsoft.management.infrastructure.dll": {}, "runtimes/win-x64/lib/netstandard1.6/microsoft.management.infrastructure.native.dll": {}}, "native": {"runtimes/win-x64/native/microsoft.management.infrastructure.native.unmanaged.dll": {}}}, "Microsoft.NET.ILLink.Tasks/8.0.17": {"type": "package", "build": {"build/Microsoft.NET.ILLink.Tasks.props": {}}}, "Microsoft.PowerShell.CoreCLR.Eventing/7.4.0": {"type": "package", "dependencies": {"System.Diagnostics.EventLog": "8.0.0"}, "compile": {"ref/net8.0/Microsoft.PowerShell.CoreCLR.Eventing.dll": {"related": ".xml"}}, "runtime": {"runtimes/win/lib/net8.0/Microsoft.PowerShell.CoreCLR.Eventing.dll": {}}}, "Microsoft.PowerShell.Native/7.4.0": {"type": "package", "native": {"runtimes/win-x64/native/PowerShell.Core.Instrumentation.dll": {}, "runtimes/win-x64/native/pwrshplugin.dll": {}}}, "Microsoft.Security.Extensions/1.2.0": {"type": "package", "compile": {"ref/netstandard2.0/getfilesiginforedistwrapper.dll": {}}, "runtime": {"runtimes/win-x64/lib/net5.0/getfilesiginforedistwrapper.dll": {}}, "native": {"runtimes/win-x64/native/getfilesiginforedist.dll": {}}}, "Microsoft.Win32.Registry.AccessControl/8.0.0": {"type": "package", "compile": {"lib/net8.0/Microsoft.Win32.Registry.AccessControl.dll": {"related": ".xml"}}, "runtime": {"runtimes/win/lib/net8.0/Microsoft.Win32.Registry.AccessControl.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Xaml.Behaviors.Wpf/1.1.39": {"type": "package", "compile": {"lib/net5.0-windows7.0/Microsoft.Xaml.Behaviors.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net5.0-windows7.0/Microsoft.Xaml.Behaviors.dll": {"related": ".pdb;.xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "Newtonsoft.Json/13.0.3": {"type": "package", "compile": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}}, "System.CodeDom/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.CodeDom.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.CodeDom.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Configuration.ConfigurationManager/8.0.0": {"type": "package", "dependencies": {"System.Diagnostics.EventLog": "8.0.0", "System.Security.Cryptography.ProtectedData": "8.0.0"}, "compile": {"lib/net8.0/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Diagnostics.DiagnosticSource/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Diagnostics.EventLog/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.Diagnostics.EventLog.dll": {"related": ".xml"}}, "runtime": {"runtimes/win/lib/net8.0/System.Diagnostics.EventLog.Messages.dll": {}, "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.DirectoryServices/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.DirectoryServices.dll": {"related": ".xml"}}, "runtime": {"runtimes/win/lib/net8.0/System.DirectoryServices.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Formats.Asn1/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.Formats.Asn1.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Formats.Asn1.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Management/8.0.0": {"type": "package", "dependencies": {"System.CodeDom": "8.0.0"}, "compile": {"lib/net8.0/System.Management.dll": {"related": ".xml"}}, "runtime": {"runtimes/win/lib/net8.0/System.Management.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Management.Automation/7.4.0": {"type": "package", "dependencies": {"Microsoft.ApplicationInsights": "2.21.0", "Microsoft.Management.Infrastructure": "3.0.0", "Microsoft.PowerShell.CoreCLR.Eventing": "7.4.0", "Microsoft.PowerShell.Native": "7.4.0", "Microsoft.Security.Extensions": "1.2.0", "Microsoft.Win32.Registry.AccessControl": "8.0.0", "Newtonsoft.Json": "13.0.3", "System.Configuration.ConfigurationManager": "8.0.0", "System.Diagnostics.DiagnosticSource": "8.0.0", "System.DirectoryServices": "8.0.0", "System.Management": "8.0.0", "System.Security.AccessControl": "6.0.2-mauipre.1.22102.15", "System.Security.Cryptography.Pkcs": "8.0.0", "System.Security.Permissions": "8.0.0", "System.Text.Encoding.CodePages": "8.0.0"}, "compile": {"ref/net8.0/System.Management.Automation.dll": {"related": ".xml"}}, "runtime": {"runtimes/win/lib/net8.0/System.Management.Automation.dll": {}}}, "System.Security.AccessControl/6.0.2-mauipre.1.22102.15": {"type": "package", "compile": {"lib/net6.0/System.Security.AccessControl.dll": {"related": ".xml"}}, "runtime": {"runtimes/win/lib/net6.0/System.Security.AccessControl.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Security.Cryptography.Pkcs/8.0.0": {"type": "package", "dependencies": {"System.Formats.Asn1": "8.0.0"}, "compile": {"lib/net8.0/System.Security.Cryptography.Pkcs.dll": {"related": ".xml"}}, "runtime": {"runtimes/win/lib/net8.0/System.Security.Cryptography.Pkcs.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Security.Cryptography.ProtectedData/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.Security.Cryptography.ProtectedData.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Security.Cryptography.ProtectedData.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Security.Permissions/8.0.0": {"type": "package", "dependencies": {"System.Windows.Extensions": "8.0.0"}, "compile": {"lib/net8.0/System.Security.Permissions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Security.Permissions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Text.Encoding.CodePages/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.Text.Encoding.CodePages.dll": {"related": ".xml"}}, "runtime": {"runtimes/win/lib/net8.0/System.Text.Encoding.CodePages.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Windows.Extensions/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.Windows.Extensions.dll": {"related": ".xml"}}, "runtime": {"runtimes/win/lib/net8.0/System.Windows.Extensions.dll": {"related": ".xml"}}}}}, "libraries": {"MaterialDesignColors/3.1.0": {"sha512": "J2mpZBWx0wArrMCK8E0Cqfsy+Wh3iRDVnznp5/84B1KcnTKI9u9Pyt2zN0oSQGsa6NhvwdUErbhE3jJd6iRTxw==", "type": "package", "path": "materialdesigncolors/3.1.0", "files": [".nupkg.metadata", ".signature.p7s", "docs/README.md", "images/MaterialDesignColors.Icon.png", "lib/net462/MaterialDesignColors.dll", "lib/net462/MaterialDesignColors.pdb", "lib/net6.0/MaterialDesignColors.dll", "lib/net6.0/MaterialDesignColors.pdb", "lib/net8.0/MaterialDesignColors.dll", "lib/net8.0/MaterialDesignColors.pdb", "materialdesigncolors.3.1.0.nupkg.sha512", "materialdesigncolors.nuspec"]}, "MaterialDesignThemes/5.0.0": {"sha512": "Az/2+YP+AdtyNceaU42cFo7Fl7KBrFkMa56uvesR363kZyOrASb0OnMvi0Ou4j1x0zwGa19AaK/vspDUBNDlBQ==", "type": "package", "path": "materialdesignthemes/5.0.0", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "build/MaterialDesignThemes.targets", "build/Resources/Roboto/Roboto-Black.ttf", "build/Resources/Roboto/Roboto-BlackItalic.ttf", "build/Resources/Roboto/Roboto-Bold.ttf", "build/Resources/Roboto/Roboto-BoldItalic.ttf", "build/Resources/Roboto/Roboto-Italic.ttf", "build/Resources/Roboto/Roboto-Light.ttf", "build/Resources/Roboto/Roboto-LightItalic.ttf", "build/Resources/Roboto/Roboto-Medium.ttf", "build/Resources/Roboto/Roboto-MediumItalic.ttf", "build/Resources/Roboto/Roboto-Regular.ttf", "build/Resources/Roboto/Roboto-Thin.ttf", "build/Resources/Roboto/Roboto-ThinItalic.ttf", "build/Resources/Roboto/RobotoCondensed-Bold.ttf", "build/Resources/Roboto/RobotoCondensed-BoldItalic.ttf", "build/Resources/Roboto/RobotoCondensed-Italic.ttf", "build/Resources/Roboto/RobotoCondensed-Light.ttf", "build/Resources/Roboto/RobotoCondensed-LightItalic.ttf", "build/Resources/Roboto/RobotoCondensed-Regular.ttf", "images/MaterialDesignThemes.Icon.png", "lib/net462/MaterialDesignThemes.Wpf.dll", "lib/net462/MaterialDesignThemes.Wpf.pdb", "lib/net462/MaterialDesignThemes.Wpf.xml", "lib/net6.0/MaterialDesignThemes.Wpf.dll", "lib/net6.0/MaterialDesignThemes.Wpf.pdb", "lib/net6.0/MaterialDesignThemes.Wpf.xml", "lib/net7.0/MaterialDesignThemes.Wpf.dll", "lib/net7.0/MaterialDesignThemes.Wpf.pdb", "lib/net7.0/MaterialDesignThemes.Wpf.xml", "materialdesignthemes.5.0.0.nupkg.sha512", "materialdesignthemes.nuspec", "tools/VisualStudioToolsManifest.xml"]}, "Microsoft.ApplicationInsights/2.21.0": {"sha512": "btZEDWAFNo9CoYliMCriSMTX3ruRGZTtYw4mo2XyyfLlowFicYVM2Xszi5evDG95QRYV7MbbH3D2RqVwfZlJHw==", "type": "package", "path": "microsoft.applicationinsights/2.21.0", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net452/Microsoft.ApplicationInsights.dll", "lib/net452/Microsoft.ApplicationInsights.pdb", "lib/net452/Microsoft.ApplicationInsights.xml", "lib/net46/Microsoft.ApplicationInsights.dll", "lib/net46/Microsoft.ApplicationInsights.pdb", "lib/net46/Microsoft.ApplicationInsights.xml", "lib/netstandard2.0/Microsoft.ApplicationInsights.dll", "lib/netstandard2.0/Microsoft.ApplicationInsights.pdb", "lib/netstandard2.0/Microsoft.ApplicationInsights.xml", "microsoft.applicationinsights.2.21.0.nupkg.sha512", "microsoft.applicationinsights.nuspec"]}, "Microsoft.Management.Infrastructure/3.0.0": {"sha512": "cGZi0q5IujCTVYKo9h22Pw+UwfZDV82HXO8HTxMG2HqntPlT3Ls8jY6punLp4YzCypJNpfCAu2kae3TIyuAiJw==", "type": "package", "path": "microsoft.management.infrastructure/3.0.0", "files": [".nupkg.metadata", ".signature.p7s", "_manifest/spdx_2.2/bsi.json", "_manifest/spdx_2.2/manifest.cat", "_manifest/spdx_2.2/manifest.spdx.json", "_manifest/spdx_2.2/manifest.spdx.json.sha256", "microsoft.management.infrastructure.3.0.0.nupkg.sha512", "microsoft.management.infrastructure.nuspec", "ref/net451/Microsoft.Management.Infrastructure.Native.dll", "ref/net451/Microsoft.Management.Infrastructure.dll", "ref/netstandard1.6/Microsoft.Management.Infrastructure.Native.dll", "ref/netstandard1.6/Microsoft.Management.Infrastructure.dll"]}, "Microsoft.Management.Infrastructure.Runtime.Unix/3.0.0": {"sha512": "QZE3uEDvZ0m7LabQvcmNOYHp7v1QPBVMpB/ild0WEE8zqUVAP5y9rRI5we37ImI1bQmW5pZ+3HNC70POPm0jBQ==", "type": "package", "path": "microsoft.management.infrastructure.runtime.unix/3.0.0", "files": [".nupkg.metadata", ".signature.p7s", "_manifest/spdx_2.2/bsi.json", "_manifest/spdx_2.2/manifest.cat", "_manifest/spdx_2.2/manifest.spdx.json", "_manifest/spdx_2.2/manifest.spdx.json.sha256", "microsoft.management.infrastructure.runtime.unix.3.0.0.nupkg.sha512", "microsoft.management.infrastructure.runtime.unix.nuspec", "runtimes/unix/lib/netstandard1.6/Microsoft.Management.Infrastructure.dll"]}, "Microsoft.Management.Infrastructure.Runtime.Win/3.0.0": {"sha512": "uwMyWN33+iQ8Wm/n1yoPXgFoiYNd0HzJyoqSVhaQZyJfaQrJR3udgcIHjqa1qbc3lS6kvfuUMN4TrF4U4refCQ==", "type": "package", "path": "microsoft.management.infrastructure.runtime.win/3.0.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "_manifest/spdx_2.2/bsi.json", "_manifest/spdx_2.2/manifest.cat", "_manifest/spdx_2.2/manifest.spdx.json", "_manifest/spdx_2.2/manifest.spdx.json.sha256", "microsoft.management.infrastructure.runtime.win.3.0.0.nupkg.sha512", "microsoft.management.infrastructure.runtime.win.nuspec", "runtimes/win-arm64/lib/net451/microsoft.management.infrastructure.dll", "runtimes/win-arm64/lib/net451/microsoft.management.infrastructure.native.dll", "runtimes/win-arm64/lib/netstandard1.6/microsoft.management.infrastructure.dll", "runtimes/win-arm64/lib/netstandard1.6/microsoft.management.infrastructure.native.dll", "runtimes/win-arm64/native/microsoft.management.infrastructure.native.unmanaged.dll", "runtimes/win-x64/lib/net451/microsoft.management.infrastructure.dll", "runtimes/win-x64/lib/net451/microsoft.management.infrastructure.native.dll", "runtimes/win-x64/lib/netstandard1.6/microsoft.management.infrastructure.dll", "runtimes/win-x64/lib/netstandard1.6/microsoft.management.infrastructure.native.dll", "runtimes/win-x64/native/microsoft.management.infrastructure.native.unmanaged.dll", "runtimes/win-x86/lib/net451/microsoft.management.infrastructure.dll", "runtimes/win-x86/lib/net451/microsoft.management.infrastructure.native.dll", "runtimes/win-x86/lib/netstandard1.6/microsoft.management.infrastructure.dll", "runtimes/win-x86/lib/netstandard1.6/microsoft.management.infrastructure.native.dll", "runtimes/win-x86/native/microsoft.management.infrastructure.native.unmanaged.dll"]}, "Microsoft.NET.ILLink.Tasks/8.0.17": {"sha512": "x5/y4l8AtshpBOrCZdlE4txw8K3e3s9meBFeZeR3l8hbbku2V7kK6ojhXvrbjg1rk3G+JqL1BI26gtgc1ZrdUw==", "type": "package", "path": "microsoft.net.illink.tasks/8.0.17", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "Sdk/Sdk.props", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/cs/ILLink.CodeFixProvider.dll", "analyzers/dotnet/cs/ILLink.RoslynAnalyzer.dll", "build/Microsoft.NET.ILLink.Analyzers.props", "build/Microsoft.NET.ILLink.Tasks.props", "build/Microsoft.NET.ILLink.targets", "microsoft.net.illink.tasks.8.0.17.nupkg.sha512", "microsoft.net.illink.tasks.nuspec", "tools/net472/ILLink.Tasks.dll", "tools/net472/ILLink.Tasks.dll.config", "tools/net472/Mono.Cecil.Mdb.dll", "tools/net472/Mono.Cecil.Pdb.dll", "tools/net472/Mono.Cecil.Rocks.dll", "tools/net472/Mono.Cecil.dll", "tools/net472/Sdk/Sdk.props", "tools/net472/System.Buffers.dll", "tools/net472/System.Collections.Immutable.dll", "tools/net472/System.Memory.dll", "tools/net472/System.Numerics.Vectors.dll", "tools/net472/System.Reflection.Metadata.dll", "tools/net472/System.Runtime.CompilerServices.Unsafe.dll", "tools/net472/build/Microsoft.NET.ILLink.Analyzers.props", "tools/net472/build/Microsoft.NET.ILLink.Tasks.props", "tools/net472/build/Microsoft.NET.ILLink.targets", "tools/net8.0/ILLink.Tasks.deps.json", "tools/net8.0/ILLink.Tasks.dll", "tools/net8.0/Mono.Cecil.Mdb.dll", "tools/net8.0/Mono.Cecil.Pdb.dll", "tools/net8.0/Mono.Cecil.Rocks.dll", "tools/net8.0/Mono.Cecil.dll", "tools/net8.0/Sdk/Sdk.props", "tools/net8.0/build/Microsoft.NET.ILLink.Analyzers.props", "tools/net8.0/build/Microsoft.NET.ILLink.Tasks.props", "tools/net8.0/build/Microsoft.NET.ILLink.targets", "tools/net8.0/illink.deps.json", "tools/net8.0/illink.dll", "tools/net8.0/illink.runtimeconfig.json", "useSharedDesignerContext.txt"]}, "Microsoft.PowerShell.CoreCLR.Eventing/7.4.0": {"sha512": "WHcqfVoaP2dZuf93GS7dk117+/CuLNCqiJN8JUhMthtJuA/lvIzblIzUf3yiEppm1QnINvF1wjy4sB1nXUuGqQ==", "type": "package", "path": "microsoft.powershell.coreclr.eventing/7.4.0", "files": [".nupkg.metadata", ".signature.p7s", "Powershell_black_64.png", "_manifest/spdx_2.2/bsi.json", "_manifest/spdx_2.2/manifest.cat", "_manifest/spdx_2.2/manifest.spdx.json", "_manifest/spdx_2.2/manifest.spdx.json.sha256", "microsoft.powershell.coreclr.eventing.7.4.0.nupkg.sha512", "microsoft.powershell.coreclr.eventing.nuspec", "ref/net8.0/Microsoft.PowerShell.CoreCLR.Eventing.dll", "ref/net8.0/Microsoft.PowerShell.CoreCLR.Eventing.xml", "runtimes/win/lib/net8.0/Microsoft.PowerShell.CoreCLR.Eventing.dll"]}, "Microsoft.PowerShell.Native/7.4.0": {"sha512": "FlaJ3JBWhqFToYT0ycMb/Xxzoof7oTQbNyI4UikgubC7AMWt5ptBNKjIAMPvOcvEHr+ohaO9GvRWp3tiyS3sKw==", "type": "package", "path": "microsoft.powershell.native/7.4.0", "files": [".nupkg.metadata", ".signature.p7s", "Powershell_black_64.png", "_manifest/spdx_2.2/bsi.json", "_manifest/spdx_2.2/manifest.cat", "_manifest/spdx_2.2/manifest.spdx.json", "_manifest/spdx_2.2/manifest.spdx.json.sha256", "microsoft.powershell.native.7.4.0.nupkg.sha512", "microsoft.powershell.native.nuspec", "runtimes/linux-arm/native/libpsl-native.so", "runtimes/linux-arm64/native/libpsl-native.so", "runtimes/linux-musl-x64/native/libpsl-native.so", "runtimes/linux-x64/native/libpsl-native.so", "runtimes/osx/native/libpsl-native.dylib", "runtimes/win-arm/native/PowerShell.Core.Instrumentation.dll", "runtimes/win-arm/native/pwrshplugin.dll", "runtimes/win-arm64/native/PowerShell.Core.Instrumentation.dll", "runtimes/win-arm64/native/pwrshplugin.dll", "runtimes/win-x64/native/PowerShell.Core.Instrumentation.dll", "runtimes/win-x64/native/pwrshplugin.dll", "runtimes/win-x86/native/PowerShell.Core.Instrumentation.dll", "runtimes/win-x86/native/pwrshplugin.dll"]}, "Microsoft.Security.Extensions/1.2.0": {"sha512": "GjHZBE5PHKrxPRyGujWQKwbKNjPQYds6HcAWKeV49X3KPgBfF2B1vV5uJey5UluyGQlvAO/DezL7WzEx9HlPQA==", "type": "package", "path": "microsoft.security.extensions/1.2.0", "files": [".nupkg.metadata", ".signature.p7s", "microsoft.security.extensions.1.2.0.nupkg.sha512", "microsoft.security.extensions.nuspec", "ref/netstandard2.0/getfilesiginforedistwrapper.dll", "runtimes/win-arm/lib/net5.0/getfilesiginforedistwrapper.dll", "runtimes/win-arm/native/getfilesiginforedist.dll", "runtimes/win-arm64/lib/net5.0/getfilesiginforedistwrapper.dll", "runtimes/win-arm64/native/getfilesiginforedist.dll", "runtimes/win-x64/lib/net5.0/getfilesiginforedistwrapper.dll", "runtimes/win-x64/native/getfilesiginforedist.dll", "runtimes/win-x86/lib/net5.0/getfilesiginforedistwrapper.dll", "runtimes/win-x86/native/getfilesiginforedist.dll"]}, "Microsoft.Win32.Registry.AccessControl/8.0.0": {"sha512": "u8PB9/v02C8mBXzl0vJ7bOyC020zOP+T1mRct+KA46DqZkB40XtsNn9pGD0QowTRsT6R4jPCghn+yAODn2UMMw==", "type": "package", "path": "microsoft.win32.registry.accesscontrol/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Win32.Registry.AccessControl.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Win32.Registry.AccessControl.targets", "lib/net462/Microsoft.Win32.Registry.AccessControl.dll", "lib/net462/Microsoft.Win32.Registry.AccessControl.xml", "lib/net6.0/Microsoft.Win32.Registry.AccessControl.dll", "lib/net6.0/Microsoft.Win32.Registry.AccessControl.xml", "lib/net7.0/Microsoft.Win32.Registry.AccessControl.dll", "lib/net7.0/Microsoft.Win32.Registry.AccessControl.xml", "lib/net8.0/Microsoft.Win32.Registry.AccessControl.dll", "lib/net8.0/Microsoft.Win32.Registry.AccessControl.xml", "lib/netstandard2.0/Microsoft.Win32.Registry.AccessControl.dll", "lib/netstandard2.0/Microsoft.Win32.Registry.AccessControl.xml", "microsoft.win32.registry.accesscontrol.8.0.0.nupkg.sha512", "microsoft.win32.registry.accesscontrol.nuspec", "runtimes/win/lib/net6.0/Microsoft.Win32.Registry.AccessControl.dll", "runtimes/win/lib/net6.0/Microsoft.Win32.Registry.AccessControl.xml", "runtimes/win/lib/net7.0/Microsoft.Win32.Registry.AccessControl.dll", "runtimes/win/lib/net7.0/Microsoft.Win32.Registry.AccessControl.xml", "runtimes/win/lib/net8.0/Microsoft.Win32.Registry.AccessControl.dll", "runtimes/win/lib/net8.0/Microsoft.Win32.Registry.AccessControl.xml", "useSharedDesignerContext.txt"]}, "Microsoft.Xaml.Behaviors.Wpf/1.1.39": {"sha512": "8PZKqw9QOcu42xk8puY4P1+EXHL9YGOR9b7qhaYx5cILHul456H073tj99vyPcCt0W0781T9RwHqkx507ZyUpQ==", "type": "package", "path": "microsoft.xaml.behaviors.wpf/1.1.39", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "lib/net45/Design/Microsoft.Xaml.Behaviors.Design.dll", "lib/net45/Microsoft.Xaml.Behaviors.dll", "lib/net45/Microsoft.Xaml.Behaviors.pdb", "lib/net45/Microsoft.Xaml.Behaviors.xml", "lib/net5.0-windows7.0/Design/Microsoft.Xaml.Behaviors.DesignTools.dll", "lib/net5.0-windows7.0/Microsoft.Xaml.Behaviors.dll", "lib/net5.0-windows7.0/Microsoft.Xaml.Behaviors.pdb", "lib/net5.0-windows7.0/Microsoft.Xaml.Behaviors.xml", "lib/netcoreapp3.1/Design/Microsoft.Xaml.Behaviors.DesignTools.dll", "lib/netcoreapp3.1/Microsoft.Xaml.Behaviors.dll", "lib/netcoreapp3.1/Microsoft.Xaml.Behaviors.pdb", "lib/netcoreapp3.1/Microsoft.Xaml.Behaviors.xml", "microsoft.xaml.behaviors.wpf.1.1.39.nupkg.sha512", "microsoft.xaml.behaviors.wpf.nuspec", "tools/Install.ps1"]}, "Newtonsoft.Json/13.0.3": {"sha512": "HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "type": "package", "path": "newtonsoft.json/13.0.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "README.md", "lib/net20/Newtonsoft.Json.dll", "lib/net20/Newtonsoft.Json.xml", "lib/net35/Newtonsoft.Json.dll", "lib/net35/Newtonsoft.Json.xml", "lib/net40/Newtonsoft.Json.dll", "lib/net40/Newtonsoft.Json.xml", "lib/net45/Newtonsoft.Json.dll", "lib/net45/Newtonsoft.Json.xml", "lib/net6.0/Newtonsoft.Json.dll", "lib/net6.0/Newtonsoft.Json.xml", "lib/netstandard1.0/Newtonsoft.Json.dll", "lib/netstandard1.0/Newtonsoft.Json.xml", "lib/netstandard1.3/Newtonsoft.Json.dll", "lib/netstandard1.3/Newtonsoft.Json.xml", "lib/netstandard2.0/Newtonsoft.Json.dll", "lib/netstandard2.0/Newtonsoft.Json.xml", "newtonsoft.json.13.0.3.nupkg.sha512", "newtonsoft.json.nuspec", "packageIcon.png"]}, "System.CodeDom/8.0.0": {"sha512": "WTlRjL6KWIMr/pAaq3rYqh0TJlzpouaQ/W1eelssHgtlwHAH25jXTkUphTYx9HaIIf7XA6qs/0+YhtLEQRkJ+Q==", "type": "package", "path": "system.codedom/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.CodeDom.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.CodeDom.targets", "lib/net462/System.CodeDom.dll", "lib/net462/System.CodeDom.xml", "lib/net6.0/System.CodeDom.dll", "lib/net6.0/System.CodeDom.xml", "lib/net7.0/System.CodeDom.dll", "lib/net7.0/System.CodeDom.xml", "lib/net8.0/System.CodeDom.dll", "lib/net8.0/System.CodeDom.xml", "lib/netstandard2.0/System.CodeDom.dll", "lib/netstandard2.0/System.CodeDom.xml", "system.codedom.8.0.0.nupkg.sha512", "system.codedom.nuspec", "useSharedDesignerContext.txt"]}, "System.Configuration.ConfigurationManager/8.0.0": {"sha512": "JlYi9XVvIREURRUlGMr1F6vOFLk7YSY4p1vHo4kX3tQ0AGrjqlRWHDi66ImHhy6qwXBG3BJ6Y1QlYQ+Qz6Xgww==", "type": "package", "path": "system.configuration.configurationmanager/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Configuration.ConfigurationManager.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Configuration.ConfigurationManager.targets", "lib/net462/System.Configuration.ConfigurationManager.dll", "lib/net462/System.Configuration.ConfigurationManager.xml", "lib/net6.0/System.Configuration.ConfigurationManager.dll", "lib/net6.0/System.Configuration.ConfigurationManager.xml", "lib/net7.0/System.Configuration.ConfigurationManager.dll", "lib/net7.0/System.Configuration.ConfigurationManager.xml", "lib/net8.0/System.Configuration.ConfigurationManager.dll", "lib/net8.0/System.Configuration.ConfigurationManager.xml", "lib/netstandard2.0/System.Configuration.ConfigurationManager.dll", "lib/netstandard2.0/System.Configuration.ConfigurationManager.xml", "system.configuration.configurationmanager.8.0.0.nupkg.sha512", "system.configuration.configurationmanager.nuspec", "useSharedDesignerContext.txt"]}, "System.Diagnostics.DiagnosticSource/8.0.0": {"sha512": "c9xLpVz6PL9lp/djOWtk5KPDZq3cSYpmXoJQY524EOtuFl5z9ZtsotpsyrDW40U1DRnQSYvcPKEUV0X//u6gkQ==", "type": "package", "path": "system.diagnostics.diagnosticsource/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Diagnostics.DiagnosticSource.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Diagnostics.DiagnosticSource.targets", "lib/net462/System.Diagnostics.DiagnosticSource.dll", "lib/net462/System.Diagnostics.DiagnosticSource.xml", "lib/net6.0/System.Diagnostics.DiagnosticSource.dll", "lib/net6.0/System.Diagnostics.DiagnosticSource.xml", "lib/net7.0/System.Diagnostics.DiagnosticSource.dll", "lib/net7.0/System.Diagnostics.DiagnosticSource.xml", "lib/net8.0/System.Diagnostics.DiagnosticSource.dll", "lib/net8.0/System.Diagnostics.DiagnosticSource.xml", "lib/netstandard2.0/System.Diagnostics.DiagnosticSource.dll", "lib/netstandard2.0/System.Diagnostics.DiagnosticSource.xml", "system.diagnostics.diagnosticsource.8.0.0.nupkg.sha512", "system.diagnostics.diagnosticsource.nuspec", "useSharedDesignerContext.txt"]}, "System.Diagnostics.EventLog/8.0.0": {"sha512": "fdYxcRjQqTTacKId/2IECojlDSFvp7LP5N78+0z/xH7v/Tuw5ZAxu23Y6PTCRinqyu2ePx+Gn1098NC6jM6d+A==", "type": "package", "path": "system.diagnostics.eventlog/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Diagnostics.EventLog.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Diagnostics.EventLog.targets", "lib/net462/System.Diagnostics.EventLog.dll", "lib/net462/System.Diagnostics.EventLog.xml", "lib/net6.0/System.Diagnostics.EventLog.dll", "lib/net6.0/System.Diagnostics.EventLog.xml", "lib/net7.0/System.Diagnostics.EventLog.dll", "lib/net7.0/System.Diagnostics.EventLog.xml", "lib/net8.0/System.Diagnostics.EventLog.dll", "lib/net8.0/System.Diagnostics.EventLog.xml", "lib/netstandard2.0/System.Diagnostics.EventLog.dll", "lib/netstandard2.0/System.Diagnostics.EventLog.xml", "runtimes/win/lib/net6.0/System.Diagnostics.EventLog.Messages.dll", "runtimes/win/lib/net6.0/System.Diagnostics.EventLog.dll", "runtimes/win/lib/net6.0/System.Diagnostics.EventLog.xml", "runtimes/win/lib/net7.0/System.Diagnostics.EventLog.Messages.dll", "runtimes/win/lib/net7.0/System.Diagnostics.EventLog.dll", "runtimes/win/lib/net7.0/System.Diagnostics.EventLog.xml", "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.Messages.dll", "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.dll", "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.xml", "system.diagnostics.eventlog.8.0.0.nupkg.sha512", "system.diagnostics.eventlog.nuspec", "useSharedDesignerContext.txt"]}, "System.DirectoryServices/8.0.0": {"sha512": "7nit//efUTy1OsAKco2f02PMrwsR2S234N0dVVp84udC77YcvpOQDz5znAWMtgMWBzY1aRJvUW61jo/7vQRfXg==", "type": "package", "path": "system.directoryservices/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.DirectoryServices.targets", "lib/net462/_._", "lib/net6.0/System.DirectoryServices.dll", "lib/net6.0/System.DirectoryServices.xml", "lib/net7.0/System.DirectoryServices.dll", "lib/net7.0/System.DirectoryServices.xml", "lib/net8.0/System.DirectoryServices.dll", "lib/net8.0/System.DirectoryServices.xml", "lib/netstandard2.0/System.DirectoryServices.dll", "lib/netstandard2.0/System.DirectoryServices.xml", "runtimes/win/lib/net6.0/System.DirectoryServices.dll", "runtimes/win/lib/net6.0/System.DirectoryServices.xml", "runtimes/win/lib/net7.0/System.DirectoryServices.dll", "runtimes/win/lib/net7.0/System.DirectoryServices.xml", "runtimes/win/lib/net8.0/System.DirectoryServices.dll", "runtimes/win/lib/net8.0/System.DirectoryServices.xml", "system.directoryservices.8.0.0.nupkg.sha512", "system.directoryservices.nuspec", "useSharedDesignerContext.txt"]}, "System.Formats.Asn1/8.0.0": {"sha512": "AJukBuLoe3QeAF+mfaRKQb2dgyrvt340iMBHYv+VdBzCUM06IxGlvl0o/uPOS7lHnXPN6u8fFRHSHudx5aTi8w==", "type": "package", "path": "system.formats.asn1/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Formats.Asn1.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Formats.Asn1.targets", "lib/net462/System.Formats.Asn1.dll", "lib/net462/System.Formats.Asn1.xml", "lib/net6.0/System.Formats.Asn1.dll", "lib/net6.0/System.Formats.Asn1.xml", "lib/net7.0/System.Formats.Asn1.dll", "lib/net7.0/System.Formats.Asn1.xml", "lib/net8.0/System.Formats.Asn1.dll", "lib/net8.0/System.Formats.Asn1.xml", "lib/netstandard2.0/System.Formats.Asn1.dll", "lib/netstandard2.0/System.Formats.Asn1.xml", "system.formats.asn1.8.0.0.nupkg.sha512", "system.formats.asn1.nuspec", "useSharedDesignerContext.txt"]}, "System.Management/8.0.0": {"sha512": "jrK22i5LRzxZCfGb+tGmke2VH7oE0DvcDlJ1HAKYU8cPmD8XnpUT0bYn2Gy98GEhGjtfbR/sxKTVb+dE770pfA==", "type": "package", "path": "system.management/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Management.targets", "lib/net462/_._", "lib/net6.0/System.Management.dll", "lib/net6.0/System.Management.xml", "lib/net7.0/System.Management.dll", "lib/net7.0/System.Management.xml", "lib/net8.0/System.Management.dll", "lib/net8.0/System.Management.xml", "lib/netstandard2.0/System.Management.dll", "lib/netstandard2.0/System.Management.xml", "runtimes/win/lib/net6.0/System.Management.dll", "runtimes/win/lib/net6.0/System.Management.xml", "runtimes/win/lib/net7.0/System.Management.dll", "runtimes/win/lib/net7.0/System.Management.xml", "runtimes/win/lib/net8.0/System.Management.dll", "runtimes/win/lib/net8.0/System.Management.xml", "system.management.8.0.0.nupkg.sha512", "system.management.nuspec", "useSharedDesignerContext.txt"]}, "System.Management.Automation/7.4.0": {"sha512": "nWsPB750tBAA6+08kcRY9fiV2eiRK6JYmySL4/IllocnA+gCUP2+sHX1enzy4uQ5DHE4SgFNv9yW+7tKX7uqsw==", "type": "package", "path": "system.management.automation/7.4.0", "files": [".nupkg.metadata", ".signature.p7s", "Powershell_black_64.png", "_manifest/spdx_2.2/bsi.json", "_manifest/spdx_2.2/manifest.cat", "_manifest/spdx_2.2/manifest.spdx.json", "_manifest/spdx_2.2/manifest.spdx.json.sha256", "ref/net8.0/System.Management.Automation.dll", "ref/net8.0/System.Management.Automation.xml", "runtimes/unix/lib/net8.0/System.Management.Automation.dll", "runtimes/win/lib/net8.0/System.Management.Automation.dll", "system.management.automation.7.4.0.nupkg.sha512", "system.management.automation.nuspec"]}, "System.Security.AccessControl/6.0.2-mauipre.1.22102.15": {"sha512": "ny0SrGGm/O1Q889Zzx1tLP8X0UjkOHjDPN0omy3onMwU1qPrPq90kWvMY8gmh6eHtRkRAGzlJlEer64ii7GMrg==", "type": "package", "path": "system.security.accesscontrol/6.0.2-mauipre.1.22102.15", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Security.AccessControl.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Security.AccessControl.dll", "lib/net461/System.Security.AccessControl.xml", "lib/net6.0/System.Security.AccessControl.dll", "lib/net6.0/System.Security.AccessControl.xml", "lib/netstandard2.0/System.Security.AccessControl.dll", "lib/netstandard2.0/System.Security.AccessControl.xml", "runtimes/win/lib/net461/System.Security.AccessControl.dll", "runtimes/win/lib/net461/System.Security.AccessControl.xml", "runtimes/win/lib/net6.0/System.Security.AccessControl.dll", "runtimes/win/lib/net6.0/System.Security.AccessControl.xml", "runtimes/win/lib/netstandard2.0/System.Security.AccessControl.dll", "runtimes/win/lib/netstandard2.0/System.Security.AccessControl.xml", "system.security.accesscontrol.6.0.2-mauipre.1.22102.15.nupkg.sha512", "system.security.accesscontrol.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Cryptography.Pkcs/8.0.0": {"sha512": "ULmp3xoOwNYjOYp4JZ2NK/6NdTgiN1GQXzVVN1njQ7LOZ0d0B9vyMnhyqbIi9Qw4JXj1JgCsitkTShboHRx7Eg==", "type": "package", "path": "system.security.cryptography.pkcs/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Security.Cryptography.Pkcs.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Security.Cryptography.Pkcs.targets", "lib/net462/System.Security.Cryptography.Pkcs.dll", "lib/net462/System.Security.Cryptography.Pkcs.xml", "lib/net6.0/System.Security.Cryptography.Pkcs.dll", "lib/net6.0/System.Security.Cryptography.Pkcs.xml", "lib/net7.0/System.Security.Cryptography.Pkcs.dll", "lib/net7.0/System.Security.Cryptography.Pkcs.xml", "lib/net8.0/System.Security.Cryptography.Pkcs.dll", "lib/net8.0/System.Security.Cryptography.Pkcs.xml", "lib/netstandard2.0/System.Security.Cryptography.Pkcs.dll", "lib/netstandard2.0/System.Security.Cryptography.Pkcs.xml", "lib/netstandard2.1/System.Security.Cryptography.Pkcs.dll", "lib/netstandard2.1/System.Security.Cryptography.Pkcs.xml", "runtimes/win/lib/net6.0/System.Security.Cryptography.Pkcs.dll", "runtimes/win/lib/net6.0/System.Security.Cryptography.Pkcs.xml", "runtimes/win/lib/net7.0/System.Security.Cryptography.Pkcs.dll", "runtimes/win/lib/net7.0/System.Security.Cryptography.Pkcs.xml", "runtimes/win/lib/net8.0/System.Security.Cryptography.Pkcs.dll", "runtimes/win/lib/net8.0/System.Security.Cryptography.Pkcs.xml", "system.security.cryptography.pkcs.8.0.0.nupkg.sha512", "system.security.cryptography.pkcs.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Cryptography.ProtectedData/8.0.0": {"sha512": "+TUFINV2q2ifyXauQXRwy4CiBhqvDEDZeVJU7qfxya4aRYOKzVBpN+4acx25VcPB9ywUN6C0n8drWl110PhZEg==", "type": "package", "path": "system.security.cryptography.protecteddata/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Security.Cryptography.ProtectedData.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Security.Cryptography.ProtectedData.targets", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net462/System.Security.Cryptography.ProtectedData.dll", "lib/net462/System.Security.Cryptography.ProtectedData.xml", "lib/net6.0/System.Security.Cryptography.ProtectedData.dll", "lib/net6.0/System.Security.Cryptography.ProtectedData.xml", "lib/net7.0/System.Security.Cryptography.ProtectedData.dll", "lib/net7.0/System.Security.Cryptography.ProtectedData.xml", "lib/net8.0/System.Security.Cryptography.ProtectedData.dll", "lib/net8.0/System.Security.Cryptography.ProtectedData.xml", "lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll", "lib/netstandard2.0/System.Security.Cryptography.ProtectedData.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "system.security.cryptography.protecteddata.8.0.0.nupkg.sha512", "system.security.cryptography.protecteddata.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Permissions/8.0.0": {"sha512": "v/BBylw7XevuAsHXoX9dDUUfmBIcUf7Lkz8K3ZXIKz3YRKpw8YftpSir4n4e/jDTKFoaK37AsC3xnk+GNFI1Ow==", "type": "package", "path": "system.security.permissions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Security.Permissions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Security.Permissions.targets", "lib/net462/System.Security.Permissions.dll", "lib/net462/System.Security.Permissions.xml", "lib/net6.0/System.Security.Permissions.dll", "lib/net6.0/System.Security.Permissions.xml", "lib/net7.0/System.Security.Permissions.dll", "lib/net7.0/System.Security.Permissions.xml", "lib/net8.0/System.Security.Permissions.dll", "lib/net8.0/System.Security.Permissions.xml", "lib/netstandard2.0/System.Security.Permissions.dll", "lib/netstandard2.0/System.Security.Permissions.xml", "system.security.permissions.8.0.0.nupkg.sha512", "system.security.permissions.nuspec", "useSharedDesignerContext.txt"]}, "System.Text.Encoding.CodePages/8.0.0": {"sha512": "OZIsVplFGaVY90G2SbpgU7EnCoOO5pw1t4ic21dBF3/1omrJFpAGoNAVpPyMVOC90/hvgkGG3VFqR13YgZMQfg==", "type": "package", "path": "system.text.encoding.codepages/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Text.Encoding.CodePages.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Text.Encoding.CodePages.targets", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net462/System.Text.Encoding.CodePages.dll", "lib/net462/System.Text.Encoding.CodePages.xml", "lib/net6.0/System.Text.Encoding.CodePages.dll", "lib/net6.0/System.Text.Encoding.CodePages.xml", "lib/net7.0/System.Text.Encoding.CodePages.dll", "lib/net7.0/System.Text.Encoding.CodePages.xml", "lib/net8.0/System.Text.Encoding.CodePages.dll", "lib/net8.0/System.Text.Encoding.CodePages.xml", "lib/netstandard2.0/System.Text.Encoding.CodePages.dll", "lib/netstandard2.0/System.Text.Encoding.CodePages.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "runtimes/win/lib/net6.0/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/net6.0/System.Text.Encoding.CodePages.xml", "runtimes/win/lib/net7.0/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/net7.0/System.Text.Encoding.CodePages.xml", "runtimes/win/lib/net8.0/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/net8.0/System.Text.Encoding.CodePages.xml", "system.text.encoding.codepages.8.0.0.nupkg.sha512", "system.text.encoding.codepages.nuspec", "useSharedDesignerContext.txt"]}, "System.Windows.Extensions/8.0.0": {"sha512": "Obg3a90MkOw9mYKxrardLpY2u0axDMrSmy4JCdq2cYbelM2cUwmUir5Bomvd1yxmPL9h5LVHU1tuKBZpUjfASg==", "type": "package", "path": "system.windows.extensions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net6.0/System.Windows.Extensions.dll", "lib/net6.0/System.Windows.Extensions.xml", "lib/net7.0/System.Windows.Extensions.dll", "lib/net7.0/System.Windows.Extensions.xml", "lib/net8.0/System.Windows.Extensions.dll", "lib/net8.0/System.Windows.Extensions.xml", "runtimes/win/lib/net6.0/System.Windows.Extensions.dll", "runtimes/win/lib/net6.0/System.Windows.Extensions.xml", "runtimes/win/lib/net7.0/System.Windows.Extensions.dll", "runtimes/win/lib/net7.0/System.Windows.Extensions.xml", "runtimes/win/lib/net8.0/System.Windows.Extensions.dll", "runtimes/win/lib/net8.0/System.Windows.Extensions.xml", "system.windows.extensions.8.0.0.nupkg.sha512", "system.windows.extensions.nuspec", "useSharedDesignerContext.txt"]}}, "projectFileDependencyGroups": {"net8.0-windows7.0": ["MaterialDesignColors >= 3.1.0", "MaterialDesignThemes >= 5.0.0", "Microsoft.NET.ILLink.Tasks >= 8.0.17", "System.Management.Automation >= 7.4.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Users\\t.gerns\\Documents\\Skripte\\Powershell\\ServerManagement-v2\\GUI\\ServerManagementApp.csproj", "projectName": "ServerManagementApp", "projectPath": "D:\\Users\\t.gerns\\Documents\\Skripte\\Powershell\\ServerManagement-v2\\GUI\\ServerManagementApp.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Users\\t.gerns\\Documents\\Skripte\\Powershell\\ServerManagement-v2\\GUI\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["D:\\Users\\t.gerns\\Documents\\Skripte\\Powershell\\ServerManagement-v2\\GUI\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"MaterialDesignColors": {"target": "Package", "version": "[3.1.0, )"}, "MaterialDesignThemes": {"target": "Package", "version": "[5.0.0, )"}, "Microsoft.NET.ILLink.Tasks": {"suppressParent": "All", "target": "Package", "version": "[8.0.17, )", "autoReferenced": true}, "System.Management.Automation": {"target": "Package", "version": "[7.4.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[8.0.17, 8.0.17]"}, {"name": "Microsoft.AspNetCore.App.Runtime.win-x64", "version": "[8.0.17, 8.0.17]"}, {"name": "Microsoft.NETCore.App.Host.win-x64", "version": "[8.0.17, 8.0.17]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[8.0.17, 8.0.17]"}, {"name": "Microsoft.NETCore.App.Runtime.win-x64", "version": "[8.0.17, 8.0.17]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[8.0.17, 8.0.17]"}, {"name": "Microsoft.WindowsDesktop.App.Runtime.win-x64", "version": "[8.0.17, 8.0.17]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.205/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"win-x64": {"#import": []}}}}