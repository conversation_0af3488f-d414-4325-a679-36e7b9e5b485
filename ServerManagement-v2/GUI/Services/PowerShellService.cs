using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Management.Automation;
using System.Management.Automation.Runspaces;
using System.Security;
using System.Threading.Tasks;
using System.Windows;
using ServerManagementApp.Models;

namespace ServerManagementApp.Services
{
    public class PowerShellService
    {
        private readonly string _modulePath;
        private readonly string _configPath;
        private PSCredential _credentials;
        private Runspace _runspace;

        public PowerShellService()
        {
            try
            {
                var appDirectory = Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly().Location);

                // Try different paths for modules and config
                var possiblePaths = new[]
                {
                    appDirectory, // Same directory as exe
                    Path.Combine(appDirectory, ".."), // Parent directory
                    Path.GetDirectoryName(appDirectory), // Parent of bin directory
                    Environment.CurrentDirectory // Current working directory
                };

                foreach (var basePath in possiblePaths)
                {
                    var modulePath = Path.Combine(basePath, "Modules", "ServerManagement.psm1");
                    var configPath = Path.Combine(basePath, "Config", "ServerConfig.psd1");

                    if (File.Exists(modulePath))
                    {
                        _modulePath = modulePath;
                        _configPath = configPath;
                        break;
                    }
                }

                InitializePowerShell();
            }
            catch (Exception ex)
            {
                // Log error but don't crash - we'll work without modules
                System.Diagnostics.Debug.WriteLine($"PowerShell initialization warning: {ex.Message}");
            }
        }

        private void InitializePowerShell()
        {
            try
            {
                var initialSessionState = InitialSessionState.CreateDefault();

                // Import our custom module if it exists
                if (!string.IsNullOrEmpty(_modulePath) && File.Exists(_modulePath))
                {
                    try
                    {
                        initialSessionState.ImportPSModule(new[] { _modulePath });
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"Warning: Could not import module {_modulePath}: {ex.Message}");
                    }
                }

                _runspace = RunspaceFactory.CreateRunspace(initialSessionState);
                _runspace.Open();
            }
            catch (Exception ex)
            {
                // Create a basic runspace without modules as fallback
                try
                {
                    _runspace = RunspaceFactory.CreateRunspace();
                    _runspace.Open();
                    System.Diagnostics.Debug.WriteLine($"PowerShell initialized without custom modules: {ex.Message}");
                }
                catch
                {
                    throw new InvalidOperationException($"Fehler beim Initialisieren von PowerShell: {ex.Message}", ex);
                }
            }
        }

        public async Task<ServerConfigItem[]> LoadServerConfigurationAsync()
        {
            return await Task.Run(() =>
            {
                try
                {
                    using var ps = PowerShell.Create();
                    ps.Runspace = _runspace;

                    ps.AddScript($"Import-PowerShellDataFile -Path '{_configPath}'");
                    var configResult = ps.Invoke();

                    if (ps.HadErrors)
                    {
                        var errors = string.Join("; ", ps.Streams.Error.Select(e => e.ToString()));
                        throw new InvalidOperationException($"PowerShell-Fehler beim Laden der Konfiguration: {errors}");
                    }

                    var config = configResult.FirstOrDefault()?.BaseObject as System.Collections.Hashtable;
                    var serverList = config?["ServerList"] as object[];

                    if (serverList == null)
                        throw new InvalidOperationException("ServerList nicht in Konfiguration gefunden");

                    var servers = new List<ServerConfigItem>();
                    foreach (System.Collections.Hashtable server in serverList)
                    {
                        servers.Add(new ServerConfigItem
                        {
                            Name = server["Name"]?.ToString(),
                            Role = server["Role"]?.ToString(),
                            Priority = Convert.ToInt32(server["Priority"]),
                            RestartDelay = Convert.ToInt32(server["RestartDelay"])
                        });
                    }

                    return servers.OrderBy(s => s.Priority).ToArray();
                }
                catch (Exception ex)
                {
                    throw new InvalidOperationException($"Fehler beim Laden der Server-Konfiguration: {ex.Message}", ex);
                }
            });
        }

        public async Task<PSCredential> GetCredentialsAsync()
        {
            if (_credentials != null)
                return _credentials;

            return await Application.Current.Dispatcher.InvokeAsync(() =>
            {
                var dialog = new Views.CredentialsDialog();
                if (dialog.ShowDialog() == true)
                {
                    _credentials = dialog.Credentials;
                    return _credentials;
                }
                return null;
            });
        }

        public async Task<ConnectivityResult> TestServerConnectivityAsync(string serverName)
        {
            return await Task.Run(() =>
            {
                try
                {
                    using var ps = PowerShell.Create();
                    ps.Runspace = _runspace;

                    // Try custom function first, fallback to basic ping
                    try
                    {
                        ps.AddCommand("Test-ServerConnectivity")
                          .AddParameter("ServerName", serverName)
                          .AddParameter("Timeout", 5);

                        var results = ps.Invoke();

                        if (!ps.HadErrors && results.Any())
                        {
                            var result = results.FirstOrDefault()?.BaseObject;
                            var psObject = result as PSObject;
                            var isOnline = (bool)(psObject?.Properties["IsOnline"]?.Value ?? false);
                            var responseTime = psObject?.Properties["ResponseTime"]?.Value as int?;
                            var ipAddress = psObject?.Properties["IPAddress"]?.Value?.ToString();

                            return new ConnectivityResult
                            {
                                ServerName = serverName,
                                IsOnline = isOnline,
                                ResponseTime = responseTime,
                                IpAddress = ipAddress,
                                Timestamp = DateTime.Now
                            };
                        }
                    }
                    catch
                    {
                        // Fallback to basic ping
                    }

                    // Fallback: Use basic PowerShell ping
                    ps.Commands.Clear();
                    ps.AddScript($@"
                        try {{
                            $ping = Test-Connection -ComputerName '{serverName}' -Count 1 -Quiet -ErrorAction Stop
                            $time = (Test-Connection -ComputerName '{serverName}' -Count 1 -ErrorAction Stop).ResponseTime
                            @{{
                                IsOnline = $ping
                                ResponseTime = $time
                                IPAddress = (Resolve-DnsName '{serverName}' -ErrorAction SilentlyContinue).IPAddress
                            }}
                        }} catch {{
                            @{{
                                IsOnline = $false
                                ResponseTime = $null
                                IPAddress = $null
                                Error = $_.Exception.Message
                            }}
                        }}
                    ");

                    var fallbackResults = ps.Invoke();

                    if (ps.HadErrors || !fallbackResults.Any())
                    {
                        return new ConnectivityResult
                        {
                            ServerName = serverName,
                            IsOnline = false,
                            Error = "Ping fehlgeschlagen",
                            Timestamp = DateTime.Now
                        };
                    }

                    var fallbackResult = fallbackResults.FirstOrDefault()?.BaseObject as System.Collections.Hashtable;
                    return new ConnectivityResult
                    {
                        ServerName = serverName,
                        IsOnline = (bool)(fallbackResult?["IsOnline"] ?? false),
                        ResponseTime = fallbackResult?["ResponseTime"] as int?,
                        IpAddress = fallbackResult?["IPAddress"]?.ToString(),
                        Error = fallbackResult?["Error"]?.ToString(),
                        Timestamp = DateTime.Now
                    };
                }
                catch (Exception ex)
                {
                    return new ConnectivityResult
                    {
                        ServerName = serverName,
                        IsOnline = false,
                        Error = ex.Message,
                        Timestamp = DateTime.Now
                    };
                }
            });
        }

        public async Task<List<UpdateInfo>> SearchWindowsUpdatesAsync(string serverName, PSCredential credentials)
        {
            return await Task.Run(() =>
            {
                try
                {
                    using var ps = PowerShell.Create();
                    ps.Runspace = _runspace;

                    ps.AddCommand("Search-WindowsUpdatesModern")
                      .AddParameter("ServerName", serverName)
                      .AddParameter("Credential", credentials);

                    var results = ps.Invoke();

                    if (ps.HadErrors)
                    {
                        var errors = string.Join("; ", ps.Streams.Error.Select(e => e.ToString()));
                        throw new InvalidOperationException($"PowerShell-Fehler: {errors}");
                    }

                    var updates = new List<UpdateInfo>();
                    foreach (var result in results)
                    {
                        var psObject = result.BaseObject as PSObject;
                        if (psObject != null)
                        {
                            updates.Add(new UpdateInfo
                            {
                                Title = psObject.Properties["Title"]?.Value?.ToString(),
                                Description = psObject.Properties["Description"]?.Value?.ToString(),
                                SizeMB = Convert.ToDouble(psObject.Properties["Size"]?.Value ?? 0),
                                IsDownloaded = (bool)(psObject.Properties["IsDownloaded"]?.Value ?? false),
                                Categories = psObject.Properties["Categories"]?.Value?.ToString(),
                                KBArticleIDs = psObject.Properties["KBArticleIDs"]?.Value?.ToString(),
                                RebootRequired = (bool)(psObject.Properties["RebootRequired"]?.Value ?? false)
                            });
                        }
                    }

                    return updates;
                }
                catch (Exception ex)
                {
                    throw new InvalidOperationException($"Fehler beim Suchen von Updates auf {serverName}: {ex.Message}", ex);
                }
            });
        }

        public async Task<UpdateInstallResult> InstallWindowsUpdatesAsync(string serverName, PSCredential credentials)
        {
            return await Task.Run(() =>
            {
                try
                {
                    using var ps = PowerShell.Create();
                    ps.Runspace = _runspace;

                    ps.AddCommand("Install-WindowsUpdatesModern")
                      .AddParameter("ServerName", serverName)
                      .AddParameter("Credential", credentials)
                      .AddParameter("NoReboot", true);

                    var results = ps.Invoke();

                    if (ps.HadErrors)
                    {
                        var errors = string.Join("; ", ps.Streams.Error.Select(e => e.ToString()));
                        throw new InvalidOperationException($"PowerShell-Fehler: {errors}");
                    }

                    var result = results.FirstOrDefault()?.BaseObject as System.Collections.Hashtable;
                    if (result == null)
                    {
                        return new UpdateInstallResult
                        {
                            Status = "Error",
                            Error = "Kein Ergebnis erhalten"
                        };
                    }

                    return new UpdateInstallResult
                    {
                        Status = result["Status"]?.ToString(),
                        UpdatesInstalled = Convert.ToInt32(result["UpdatesInstalled"] ?? 0),
                        RebootRequired = (bool)(result["RebootRequired"] ?? false)
                    };
                }
                catch (Exception ex)
                {
                    return new UpdateInstallResult
                    {
                        Status = "Error",
                        Error = ex.Message
                    };
                }
            });
        }

        public async Task<RestartResult> RestartServerAsync(string serverName, PSCredential credentials)
        {
            return await Task.Run(() =>
            {
                try
                {
                    using var ps = PowerShell.Create();
                    ps.Runspace = _runspace;

                    ps.AddCommand("Restart-ServerSafe")
                      .AddParameter("ServerName", serverName)
                      .AddParameter("Credential", credentials)
                      .AddParameter("TimeoutSeconds", 600);

                    var results = ps.Invoke();

                    if (ps.HadErrors)
                    {
                        var errors = string.Join("; ", ps.Streams.Error.Select(e => e.ToString()));
                        return new RestartResult
                        {
                            ServerName = serverName,
                            Success = false,
                            Error = errors
                        };
                    }

                    var result = results.FirstOrDefault()?.BaseObject as System.Collections.Hashtable;
                    if (result == null)
                    {
                        return new RestartResult
                        {
                            ServerName = serverName,
                            Success = false,
                            Error = "Kein Ergebnis erhalten"
                        };
                    }

                    return new RestartResult
                    {
                        ServerName = serverName,
                        Success = (bool)(result["RestartSuccessful"] ?? false),
                        TotalTimeSeconds = Convert.ToDouble(result["TotalTime"] ?? 0),
                        Error = result["Error"]?.ToString()
                    };
                }
                catch (Exception ex)
                {
                    return new RestartResult
                    {
                        ServerName = serverName,
                        Success = false,
                        Error = ex.Message
                    };
                }
            });
        }

        public async Task RestartAllServersOrderedAsync(PSCredential credentials)
        {
            await Task.Run(() =>
            {
                try
                {
                    using var ps = PowerShell.Create();
                    ps.Runspace = _runspace;

                    ps.AddCommand("Restart-AllServersOrdered")
                      .AddParameter("Credential", credentials);

                    ps.Invoke();

                    if (ps.HadErrors)
                    {
                        var errors = string.Join("; ", ps.Streams.Error.Select(e => e.ToString()));
                        throw new InvalidOperationException($"PowerShell-Fehler: {errors}");
                    }
                }
                catch (Exception ex)
                {
                    throw new InvalidOperationException($"Fehler beim geordneten Neustart aller Server: {ex.Message}", ex);
                }
            });
        }

        public async Task<ServerDetails> GetServerDetailsAsync(string serverName, PSCredential credentials)
        {
            return await Task.Run(() =>
            {
                try
                {
                    using var ps = PowerShell.Create();
                    ps.Runspace = _runspace;

                    // Get basic system information
                    ps.AddScript($@"
                        Invoke-Command -ComputerName '{serverName}' -Credential $args[0] -ScriptBlock {{
                            $os = Get-WmiObject -Class Win32_OperatingSystem
                            $computer = Get-WmiObject -Class Win32_ComputerSystem
                            $disks = Get-WmiObject -Class Win32_LogicalDisk | Where-Object {{ $_.DriveType -eq 3 -and $_.Size -gt 0 }}
                            $services = Get-Service | Where-Object {{ $_.Status -eq 'Stopped' -and $_.StartType -eq 'Automatic' }} | Select-Object -First 10

                            return @{{
                                OSName = $os.Caption
                                OSVersion = $os.Version
                                LastBootTime = $os.ConvertToDateTime($os.LastBootUpTime)
                                TotalMemory = [math]::Round($computer.TotalPhysicalMemory / 1GB, 1)
                                FreeMemory = [math]::Round($os.FreePhysicalMemory / 1KB / 1MB, 0)
                                Manufacturer = $computer.Manufacturer
                                Model = $computer.Model
                                Disks = $disks | ForEach-Object {{
                                    @{{
                                        Drive = $_.DeviceID
                                        Label = $_.VolumeName
                                        FileSystem = $_.FileSystem
                                        TotalSpace = [math]::Round($_.Size / 1GB, 1)
                                        FreeSpace = [math]::Round($_.FreeSpace / 1GB, 1)
                                        UsedPercent = [math]::Round((($_.Size - $_.FreeSpace) / $_.Size) * 100, 1)
                                    }}
                                }}
                                StoppedServices = $services | Select-Object Name, DisplayName
                            }}
                        }}
                    ");

                    ps.AddArgument(credentials);
                    var results = ps.Invoke();

                    if (ps.HadErrors)
                    {
                        var errors = string.Join("; ", ps.Streams.Error.Select(e => e.ToString()));
                        throw new InvalidOperationException($"PowerShell-Fehler: {errors}");
                    }

                    var result = results.FirstOrDefault()?.BaseObject as System.Collections.Hashtable;
                    if (result == null)
                        throw new InvalidOperationException("Keine Server-Details erhalten");

                    // Convert to ServerDetails object
                    var details = new ServerDetails
                    {
                        OperatingSystem = result["OSName"]?.ToString(),
                        Version = result["OSVersion"]?.ToString(),
                        LastBootTime = (DateTime)result["LastBootTime"],
                        TotalMemoryGB = Convert.ToDouble(result["TotalMemory"]),
                        FreeMemoryMB = Convert.ToDouble(result["FreeMemory"]),
                        Manufacturer = result["Manufacturer"]?.ToString(),
                        Model = result["Model"]?.ToString()
                    };

                    // Convert disks
                    var disksArray = result["Disks"] as object[];
                    if (disksArray != null)
                    {
                        var diskList = new List<DiskInfo>();
                        foreach (System.Collections.Hashtable disk in disksArray)
                        {
                            diskList.Add(new DiskInfo
                            {
                                Drive = disk["Drive"]?.ToString(),
                                Label = disk["Label"]?.ToString(),
                                FileSystem = disk["FileSystem"]?.ToString(),
                                TotalSpaceGB = Convert.ToDouble(disk["TotalSpace"]),
                                FreeSpaceGB = Convert.ToDouble(disk["FreeSpace"]),
                                UsedPercent = Convert.ToDouble(disk["UsedPercent"]),
                                DriveType = "Festplatte"
                            });
                        }
                        details.Disks = diskList.ToArray();
                    }

                    // Convert services
                    var servicesArray = result["StoppedServices"] as object[];
                    if (servicesArray != null)
                    {
                        var serviceList = new List<ServiceInfo>();
                        foreach (var service in servicesArray)
                        {
                            var psService = service as PSObject;
                            serviceList.Add(new ServiceInfo
                            {
                                Name = psService?.Properties["Name"]?.Value?.ToString(),
                                DisplayName = psService?.Properties["DisplayName"]?.Value?.ToString(),
                                Status = "Stopped",
                                StartType = "Automatic"
                            });
                        }
                        details.StoppedServices = serviceList.ToArray();
                    }

                    return details;
                }
                catch (Exception ex)
                {
                    throw new InvalidOperationException($"Fehler beim Abrufen der Server-Details für {serverName}: {ex.Message}", ex);
                }
            });
        }

        public void Dispose()
        {
            _runspace?.Dispose();
        }
    }
}
