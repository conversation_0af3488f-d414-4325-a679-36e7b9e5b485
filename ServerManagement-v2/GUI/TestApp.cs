using System;
using System.Windows;

namespace ServerManagementApp
{
    public class TestApp
    {
        [STAThread]
        public static void Main()
        {
            try
            {
                Console.WriteLine("Starting WPF Application...");
                
                var app = new Application();
                var window = new MainWindow();
                
                Console.WriteLine("MainWindow created successfully");
                
                app.Run(window);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
                Console.WriteLine($"Stack Trace: {ex.StackTrace}");
                Console.ReadKey();
            }
        }
    }
}
