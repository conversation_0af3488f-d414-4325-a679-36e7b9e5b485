<Application x:Class="ServerManagementApp.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             StartupUri="MainWindow.xaml">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- Material Design -->
                <materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="Blue" SecondaryColor="Cyan" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
            </ResourceDictionary.MergedDictionaries>
            <!-- Server Status Colors -->
            <SolidColorBrush x:Key="OnlineBrush" Color="#4CAF50"/>
            <SolidColorBrush x:Key="OfflineBrush" Color="#F44336"/>
            <SolidColorBrush x:Key="WarningBrush" Color="#FF9800"/>
            <SolidColorBrush x:Key="InfoBrush" Color="#2196F3"/>
            <SolidColorBrush x:Key="PrimaryBrush" Color="#2196F3"/>
            <SolidColorBrush x:Key="SecondaryBrush" Color="#03DAC6"/>
            <SolidColorBrush x:Key="BackgroundBrush" Color="#FAFAFA"/>
            <SolidColorBrush x:Key="SurfaceBrush" Color="#FFFFFF"/>

            <!-- Card Style -->
            <Style x:Key="ServerCard" TargetType="Border">
                <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
                <Setter Property="BorderBrush" Value="#E0E0E0"/>
                <Setter Property="BorderThickness" Value="1"/>
                <Setter Property="CornerRadius" Value="8"/>
                <Setter Property="Margin" Value="8"/>
                <Setter Property="Padding" Value="16"/>
                <Setter Property="Effect">
                    <Setter.Value>
                        <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="2" BlurRadius="8"/>
                    </Setter.Value>
                </Setter>
            </Style>

            <!-- Server Name Style -->
            <Style x:Key="ServerName" TargetType="TextBlock">
                <Setter Property="FontSize" Value="18"/>
                <Setter Property="FontWeight" Value="SemiBold"/>
                <Setter Property="Margin" Value="8,0"/>
                <Setter Property="Foreground" Value="#212121"/>
            </Style>

            <!-- Server Role Style -->
            <Style x:Key="ServerRole" TargetType="TextBlock">
                <Setter Property="FontSize" Value="12"/>
                <Setter Property="Foreground" Value="#757575"/>
                <Setter Property="Margin" Value="8,0"/>
            </Style>

            <!-- Action Button Style -->
            <Style x:Key="ActionButton" TargetType="Button">
                <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
                <Setter Property="Foreground" Value="White"/>
                <Setter Property="BorderThickness" Value="0"/>
                <Setter Property="Padding" Value="16,8"/>
                <Setter Property="Margin" Value="4"/>
                <Setter Property="FontWeight" Value="Medium"/>
                <Setter Property="Cursor" Value="Hand"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="Button">
                            <Border Background="{TemplateBinding Background}"
                                    CornerRadius="4"
                                    Padding="{TemplateBinding Padding}">
                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="#1976D2"/>
                                </Trigger>
                                <Trigger Property="IsPressed" Value="True">
                                    <Setter Property="Background" Value="#1565C0"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>

            <!-- Secondary Button Style -->
            <Style x:Key="SecondaryButton" TargetType="Button" BasedOn="{StaticResource ActionButton}">
                <Setter Property="Background" Value="#E0E0E0"/>
                <Setter Property="Foreground" Value="#424242"/>
            </Style>

            <!-- Icon Button Style -->
            <Style x:Key="IconButton" TargetType="Button">
                <Setter Property="Background" Value="Transparent"/>
                <Setter Property="BorderThickness" Value="0"/>
                <Setter Property="Width" Value="40"/>
                <Setter Property="Height" Value="40"/>
                <Setter Property="Cursor" Value="Hand"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="Button">
                            <Border Background="{TemplateBinding Background}"
                                    CornerRadius="20"
                                    Padding="8">
                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="#F5F5F5"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>
        </ResourceDictionary>
    </Application.Resources>
</Application>
