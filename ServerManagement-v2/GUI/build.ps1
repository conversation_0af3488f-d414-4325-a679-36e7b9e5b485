#Requires -Version 5.1
<#
.SYNOPSIS
    Build-Skript für die Windows Server Management GUI-Anwendung
.DESCRIPTION
    Kompiliert die WPF-Anwendung als Single-File Deployment
#>

param(
    [ValidateSet('Debug', 'Release')]
    [string]$Configuration = 'Release',

    [switch]$Clean,

    [switch]$Run
)

$ErrorActionPreference = 'Stop'

# Pfade
$ProjectPath = $PSScriptRoot
$ProjectFile = Join-Path $ProjectPath "ServerManagementApp.csproj"
$OutputPath = Join-Path $ProjectPath "bin\$Configuration"
$PublishPath = Join-Path $ProjectPath "publish"

Write-Host "🚀 Windows Server Management GUI Build" -ForegroundColor Cyan
Write-Host "=======================================" -ForegroundColor Cyan
Write-Host ""

# Prüfe .NET 8 Installation
try {
    $dotnetVersion = dotnet --version
    Write-Host "✓ .NET Version: $dotnetVersion" -ForegroundColor Green
} catch {
    Write-Error ".NET 8 SDK ist nicht installiert. Bitte installieren Sie .NET 8 SDK von https://dotnet.microsoft.com/download"
    exit 1
}

# Clean Build
if ($Clean) {
    Write-Host "🧹 Bereinige Build-Verzeichnisse..." -ForegroundColor Yellow

    if (Test-Path $OutputPath) {
        Remove-Item $OutputPath -Recurse -Force
        Write-Host "  ✓ $OutputPath gelöscht" -ForegroundColor Gray
    }

    if (Test-Path $PublishPath) {
        Remove-Item $PublishPath -Recurse -Force
        Write-Host "  ✓ $PublishPath gelöscht" -ForegroundColor Gray
    }

    dotnet clean $ProjectFile --configuration $Configuration --verbosity quiet
    Write-Host "  ✓ Projekt bereinigt" -ForegroundColor Gray
    Write-Host ""
}

# Restore NuGet Packages
Write-Host "📦 Lade NuGet-Pakete..." -ForegroundColor Yellow
dotnet restore $ProjectFile --verbosity quiet
if ($LASTEXITCODE -ne 0) {
    Write-Host "⚠️  Warnung: Einige NuGet-Pakete konnten nicht geladen werden" -ForegroundColor Yellow
    Write-Host "  Versuche Build trotzdem..." -ForegroundColor Gray
} else {
    Write-Host "  ✓ NuGet-Pakete geladen" -ForegroundColor Green
}
Write-Host ""

# Build
Write-Host "🔨 Kompiliere Anwendung..." -ForegroundColor Yellow
dotnet build $ProjectFile --configuration $Configuration --no-restore --verbosity quiet
if ($LASTEXITCODE -ne 0) {
    Write-Error "Fehler beim Kompilieren der Anwendung"
    exit 1
}
Write-Host "  ✓ Anwendung erfolgreich kompiliert" -ForegroundColor Green
Write-Host ""

# Publish (Single-File Deployment)
Write-Host "📋 Erstelle Single-File Anwendung..." -ForegroundColor Yellow

dotnet publish $ProjectFile `
    --configuration $Configuration `
    --output $PublishPath `
    --verbosity quiet

if ($LASTEXITCODE -ne 0) {
    Write-Error "Fehler beim Erstellen der Anwendung"
    exit 1
}

Write-Host "  ✓ Single-File Anwendung erstellt" -ForegroundColor Green
Write-Host "  📁 Ausgabepfad: $PublishPath" -ForegroundColor Gray
Write-Host ""

# Ergebnis anzeigen
Write-Host "🎉 Build erfolgreich abgeschlossen!" -ForegroundColor Green
Write-Host ""

$exePath = Join-Path $PublishPath "ServerManagementApp.exe"
if (Test-Path $exePath) {
    Write-Host "📋 ANWENDUNG BEREIT:" -ForegroundColor Cyan
    Write-Host "  Ausführbare Datei: $exePath" -ForegroundColor White
    Write-Host "  Größe: $([math]::Round((Get-Item $exePath).Length / 1MB, 1)) MB" -ForegroundColor Gray
    Write-Host ""

    if ($Run) {
        Write-Host "🚀 Starte Anwendung..." -ForegroundColor Cyan
        Start-Process $exePath
    } else {
        Write-Host "🚀 Starten Sie die Anwendung mit:" -ForegroundColor Cyan
        Write-Host "  $exePath" -ForegroundColor Yellow
        Write-Host "  oder: .\build.ps1 -Run" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "📚 Optionen:" -ForegroundColor Cyan
Write-Host "  .\build.ps1 -Clean    # Bereinigt Build-Verzeichnisse" -ForegroundColor Gray
Write-Host "  .\build.ps1 -Run      # Kompiliert und startet die Anwendung" -ForegroundColor Gray
