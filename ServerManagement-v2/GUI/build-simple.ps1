param(
    [switch]$Clean,
    [switch]$Run
)

$ErrorActionPreference = 'Stop'

Write-Host "🚀 Windows Server Management Build" -ForegroundColor Cyan
Write-Host "==================================" -ForegroundColor Cyan
Write-Host ""

# Pfade
$ProjectPath = $PSScriptRoot
$ProjectFile = Join-Path $ProjectPath "ServerManagementApp.csproj"
$PublishPath = Join-Path $ProjectPath "publish"

# Prüfe .NET
try {
    $dotnetVersion = dotnet --version
    Write-Host "✓ .NET Version: $dotnetVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ .NET 8 ist nicht installiert!" -ForegroundColor Red
    exit 1
}

# Clean
if ($Clean) {
    Write-Host "🧹 Bereinige..." -ForegroundColor Yellow
    if (Test-Path $PublishPath) {
        Remove-Item $PublishPath -Recurse -Force
    }
    dotnet clean $ProjectFile --verbosity quiet
    Write-Host "✓ Bereinigt" -ForegroundColor Green
}

# Build
Write-Host "🔨 Kompiliere..." -ForegroundColor Yellow
dotnet publish $ProjectFile --configuration Release --output $PublishPath --verbosity quiet

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Build fehlgeschlagen" -ForegroundColor Red
    exit 1
}

# Ergebnis
$exePath = Join-Path $PublishPath "ServerManagementApp.exe"
if (Test-Path $exePath) {
    $size = [math]::Round((Get-Item $exePath).Length / 1MB, 1)
    Write-Host "✓ Build erfolgreich - $size MB" -ForegroundColor Green
    Write-Host "📁 $exePath" -ForegroundColor Gray
    
    if ($Run) {
        Write-Host "🚀 Starte Anwendung..." -ForegroundColor Cyan
        Start-Process $exePath
    }
} else {
    Write-Host "❌ Exe nicht gefunden" -ForegroundColor Red
    exit 1
}
