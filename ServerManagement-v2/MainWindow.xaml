<Window x:Class="SimpleServerManager.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="Windows Server Management v2.0" 
        Height="800" Width="1200"
        WindowStartupLocation="CenterScreen"
        Background="#FAFAFA">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <materialDesign:Card Grid.Row="0" Margin="16,16,16,8" Padding="24">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0">
                    <TextBlock Text="Windows Server Management v2.0" 
                               Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                               Foreground="{StaticResource PrimaryHueMidBrush}"/>
                    <TextBlock Text="Moderne Server-Verwaltung für Windows-Umgebungen" 
                               Style="{StaticResource MaterialDesignBody2TextBlock}"
                               Opacity="0.7"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <TextBlock x:Name="TimeText" 
                               Style="{StaticResource MaterialDesignBody1TextBlock}"
                               VerticalAlignment="Center"
                               Margin="0,0,16,0"/>
                    <Button Style="{StaticResource MaterialDesignIconButton}"
                            ToolTip="Aktualisieren"
                            Click="RefreshButton_Click">
                        <materialDesign:PackIcon Kind="Refresh" Width="24" Height="24"/>
                    </Button>
                </StackPanel>
            </Grid>
        </materialDesign:Card>
        
        <!-- Main Content -->
        <Grid Grid.Row="1" Margin="16,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="2*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            
            <!-- Server Overview -->
            <materialDesign:Card Grid.Column="0" Margin="0,0,8,0" Padding="16">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <TextBlock Grid.Row="0" Text="Server-Übersicht" 
                               Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                               Margin="0,0,0,16"/>
                    
                    <ScrollViewer Grid.Row="1">
                        <ItemsControl x:Name="ServerList">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Border Style="{StaticResource ServerCard}">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>
                                            
                                            <materialDesign:PackIcon Grid.Column="0" 
                                                                     Kind="Server" 
                                                                     Width="32" Height="32"
                                                                     VerticalAlignment="Center"
                                                                     Margin="0,0,16,0"
                                                                     Foreground="{StaticResource PrimaryHueMidBrush}"/>
                                            
                                            <StackPanel Grid.Column="1">
                                                <TextBlock Text="{Binding Name}" 
                                                           Style="{StaticResource MaterialDesignSubtitle1TextBlock}"/>
                                                <TextBlock Text="{Binding Role}" 
                                                           Style="{StaticResource MaterialDesignBody2TextBlock}"
                                                           Opacity="0.7"/>
                                                <TextBlock Text="{Binding Status}" 
                                                           Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                                           Foreground="{Binding StatusColor}"/>
                                            </StackPanel>
                                            
                                            <StackPanel Grid.Column="2" Orientation="Horizontal">
                                                <Button Style="{StaticResource MaterialDesignIconButton}"
                                                        ToolTip="Ping"
                                                        Click="PingServer_Click"
                                                        Tag="{Binding Name}">
                                                    <materialDesign:PackIcon Kind="Wifi" Width="20" Height="20"/>
                                                </Button>
                                                <Button Style="{StaticResource MaterialDesignIconButton}"
                                                        ToolTip="Neustart"
                                                        Click="RestartServer_Click"
                                                        Tag="{Binding Name}">
                                                    <materialDesign:PackIcon Kind="Restart" Width="20" Height="20"/>
                                                </Button>
                                            </StackPanel>
                                        </Grid>
                                    </Border>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </ScrollViewer>
                </Grid>
            </materialDesign:Card>
            
            <!-- Actions Panel -->
            <materialDesign:Card Grid.Column="1" Margin="8,0,0,0" Padding="16">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <TextBlock Grid.Row="0" Text="Aktionen" 
                               Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                               Margin="0,0,0,16"/>
                    
                    <StackPanel Grid.Row="1">
                        <Button Style="{StaticResource MaterialDesignRaisedButton}"
                                Content="Alle Server pingen"
                                Click="PingAllServers_Click"
                                Margin="0,4"/>
                        <Button Style="{StaticResource MaterialDesignRaisedButton}"
                                Content="Server neustarten"
                                Click="RestartAllServers_Click"
                                Margin="0,4"/>
                        <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                                Content="Updates suchen"
                                Click="SearchUpdates_Click"
                                Margin="0,4"/>
                        <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                                Content="Einstellungen"
                                Click="Settings_Click"
                                Margin="0,4"/>
                    </StackPanel>
                    
                    <!-- Log -->
                    <Grid Grid.Row="2" Margin="0,24,0,0">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>
                        
                        <TextBlock Grid.Row="0" Text="Aktivitätsprotokoll" 
                                   Style="{StaticResource MaterialDesignSubtitle2TextBlock}"
                                   Margin="0,0,0,8"/>
                        
                        <ScrollViewer Grid.Row="1" 
                                      Background="#F5F5F5" 
                                      Padding="8">
                            <TextBlock x:Name="LogTextBlock" 
                                       FontFamily="Consolas"
                                       FontSize="11"
                                       TextWrapping="Wrap"/>
                        </ScrollViewer>
                    </Grid>
                </Grid>
            </materialDesign:Card>
        </Grid>
        
        <!-- Status Bar -->
        <materialDesign:Card Grid.Row="2" Margin="16,8,16,16" Padding="16,8">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock x:Name="StatusText" 
                           Text="Bereit" 
                           Style="{StaticResource MaterialDesignBody2TextBlock}"
                           VerticalAlignment="Center"/>
                
                <ProgressBar x:Name="ProgressBar" 
                             Grid.Column="1"
                             Width="200" 
                             Height="4"
                             IsIndeterminate="True"
                             Visibility="Collapsed"/>
            </Grid>
        </materialDesign:Card>
    </Grid>
</Window>
