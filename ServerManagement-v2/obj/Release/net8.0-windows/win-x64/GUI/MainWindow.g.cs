#pragma checksum "..\..\..\..\..\GUI\MainWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "043C01737F9ADC3DC058802B6AE1F82F25F6BB68"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace ServerManagementApp {
    
    
    /// <summary>
    /// MainWindow
    /// </summary>
    public partial class MainWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 36 "..\..\..\..\..\GUI\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CredentialsButton;
        
        #line default
        #line hidden
        
        
        #line 42 "..\..\..\..\..\GUI\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SettingsButton;
        
        #line default
        #line hidden
        
        
        #line 48 "..\..\..\..\..\GUI\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshButton;
        
        #line default
        #line hidden
        
        
        #line 74 "..\..\..\..\..\GUI\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PingAllButton;
        
        #line default
        #line hidden
        
        
        #line 82 "..\..\..\..\..\GUI\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SearchUpdatesButton;
        
        #line default
        #line hidden
        
        
        #line 90 "..\..\..\..\..\GUI\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button InstallUpdatesButton;
        
        #line default
        #line hidden
        
        
        #line 98 "..\..\..\..\..\GUI\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RestartServersButton;
        
        #line default
        #line hidden
        
        
        #line 106 "..\..\..\..\..\GUI\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ServerStatusButton;
        
        #line default
        #line hidden
        
        
        #line 120 "..\..\..\..\..\GUI\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox SelectAllCheckBox;
        
        #line default
        #line hidden
        
        
        #line 124 "..\..\..\..\..\GUI\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ItemsControl ServerSelectionList;
        
        #line default
        #line hidden
        
        
        #line 139 "..\..\..\..\..\GUI\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TabControl MainTabControl;
        
        #line default
        #line hidden
        
        
        #line 143 "..\..\..\..\..\GUI\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ItemsControl ServerOverviewList;
        
        #line default
        #line hidden
        
        
        #line 203 "..\..\..\..\..\GUI\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ClearLogButton;
        
        #line default
        #line hidden
        
        
        #line 206 "..\..\..\..\..\GUI\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveLogButton;
        
        #line default
        #line hidden
        
        
        #line 214 "..\..\..\..\..\GUI\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ScrollViewer LogScrollViewer;
        
        #line default
        #line hidden
        
        
        #line 215 "..\..\..\..\..\GUI\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox LogTextBox;
        
        #line default
        #line hidden
        
        
        #line 240 "..\..\..\..\..\GUI\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusText;
        
        #line default
        #line hidden
        
        
        #line 244 "..\..\..\..\..\GUI\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ProgressBar ProgressBar;
        
        #line default
        #line hidden
        
        
        #line 248 "..\..\..\..\..\GUI\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TimeText;
        
        #line default
        #line hidden
        
        
        #line 255 "..\..\..\..\..\GUI\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid LoadingOverlay;
        
        #line default
        #line hidden
        
        
        #line 266 "..\..\..\..\..\GUI\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LoadingText;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SimpleServerManager;V2.0.0.0;component/gui/mainwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\GUI\MainWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.CredentialsButton = ((System.Windows.Controls.Button)(target));
            
            #line 39 "..\..\..\..\..\GUI\MainWindow.xaml"
            this.CredentialsButton.Click += new System.Windows.RoutedEventHandler(this.CredentialsButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.SettingsButton = ((System.Windows.Controls.Button)(target));
            
            #line 45 "..\..\..\..\..\GUI\MainWindow.xaml"
            this.SettingsButton.Click += new System.Windows.RoutedEventHandler(this.SettingsButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.RefreshButton = ((System.Windows.Controls.Button)(target));
            
            #line 51 "..\..\..\..\..\GUI\MainWindow.xaml"
            this.RefreshButton.Click += new System.Windows.RoutedEventHandler(this.RefreshButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.PingAllButton = ((System.Windows.Controls.Button)(target));
            
            #line 75 "..\..\..\..\..\GUI\MainWindow.xaml"
            this.PingAllButton.Click += new System.Windows.RoutedEventHandler(this.PingAllButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.SearchUpdatesButton = ((System.Windows.Controls.Button)(target));
            
            #line 83 "..\..\..\..\..\GUI\MainWindow.xaml"
            this.SearchUpdatesButton.Click += new System.Windows.RoutedEventHandler(this.SearchUpdatesButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.InstallUpdatesButton = ((System.Windows.Controls.Button)(target));
            
            #line 91 "..\..\..\..\..\GUI\MainWindow.xaml"
            this.InstallUpdatesButton.Click += new System.Windows.RoutedEventHandler(this.InstallUpdatesButton_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.RestartServersButton = ((System.Windows.Controls.Button)(target));
            
            #line 99 "..\..\..\..\..\GUI\MainWindow.xaml"
            this.RestartServersButton.Click += new System.Windows.RoutedEventHandler(this.RestartServersButton_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.ServerStatusButton = ((System.Windows.Controls.Button)(target));
            
            #line 107 "..\..\..\..\..\GUI\MainWindow.xaml"
            this.ServerStatusButton.Click += new System.Windows.RoutedEventHandler(this.ServerStatusButton_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.SelectAllCheckBox = ((System.Windows.Controls.CheckBox)(target));
            
            #line 121 "..\..\..\..\..\GUI\MainWindow.xaml"
            this.SelectAllCheckBox.Checked += new System.Windows.RoutedEventHandler(this.SelectAllCheckBox_Checked);
            
            #line default
            #line hidden
            
            #line 122 "..\..\..\..\..\GUI\MainWindow.xaml"
            this.SelectAllCheckBox.Unchecked += new System.Windows.RoutedEventHandler(this.SelectAllCheckBox_Unchecked);
            
            #line default
            #line hidden
            return;
            case 10:
            this.ServerSelectionList = ((System.Windows.Controls.ItemsControl)(target));
            return;
            case 11:
            this.MainTabControl = ((System.Windows.Controls.TabControl)(target));
            return;
            case 12:
            this.ServerOverviewList = ((System.Windows.Controls.ItemsControl)(target));
            return;
            case 15:
            this.ClearLogButton = ((System.Windows.Controls.Button)(target));
            
            #line 205 "..\..\..\..\..\GUI\MainWindow.xaml"
            this.ClearLogButton.Click += new System.Windows.RoutedEventHandler(this.ClearLogButton_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            this.SaveLogButton = ((System.Windows.Controls.Button)(target));
            
            #line 208 "..\..\..\..\..\GUI\MainWindow.xaml"
            this.SaveLogButton.Click += new System.Windows.RoutedEventHandler(this.SaveLogButton_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            this.LogScrollViewer = ((System.Windows.Controls.ScrollViewer)(target));
            return;
            case 18:
            this.LogTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 19:
            this.StatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 20:
            this.ProgressBar = ((System.Windows.Controls.ProgressBar)(target));
            return;
            case 21:
            this.TimeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 22:
            this.LoadingOverlay = ((System.Windows.Controls.Grid)(target));
            return;
            case 23:
            this.LoadingText = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 13:
            
            #line 174 "..\..\..\..\..\GUI\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ShowServerDetails_Click);
            
            #line default
            #line hidden
            break;
            case 14:
            
            #line 180 "..\..\..\..\..\GUI\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.RestartSingleServer_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

