using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Management.Automation;
using System.Management.Automation.Runspaces;
using System.Net.NetworkInformation;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Threading;
using MaterialDesignThemes.Wpf;

namespace SimpleServerGUI
{
    public partial class MainWindow : Window
    {
        private readonly DispatcherTimer _timer;
        private readonly ObservableCollection<ServerItem> _servers;
        private readonly ObservableCollection<ServerSelectionItem> _serverSelection;
        private Runspace _runspace;

        public MainWindow()
        {
            InitializeComponent();
            
            _servers = new ObservableCollection<ServerItem>();
            _serverSelection = new ObservableCollection<ServerSelectionItem>();
            
            ServerOverviewList.ItemsSource = _servers;
            ServerSelectionList.ItemsSource = _serverSelection;
            
            // Timer for status bar
            _timer = new DispatcherTimer();
            _timer.Interval = TimeSpan.FromSeconds(1);
            _timer.Tick += Timer_Tick;
            _timer.Start();
            
            // Initialize
            Loaded += MainWindow_Loaded;
        }

        private async void MainWindow_Loaded(object sender, RoutedEventArgs e)
        {
            AddLogEntry("Windows Server Management v2.0 gestartet");
            AddLogEntry("Moderne GUI mit Material Design geladen");
            
            await InitializeAsync();
        }

        private async Task InitializeAsync()
        {
            ShowLoading("Initialisiere Server Management...");
            
            try
            {
                // Initialize PowerShell
                InitializePowerShell();
                
                // Initialize servers
                InitializeServers();
                
                AddLogEntry($"{_servers.Count} Server konfiguriert");
                
                // Auto-start ping test
                AddLogEntry("Starte automatischen Ping-Test...");
                await PerformPingTestAsync();
                
                // Load disk space information
                await LoadDiskSpaceAsync();
                
                StatusText.Text = $"{_servers.Count} Server geladen - Bereit";
            }
            catch (Exception ex)
            {
                AddLogEntry($"Fehler bei der Initialisierung: {ex.Message}");
                StatusText.Text = "Initialisierung fehlgeschlagen";
            }
            finally
            {
                HideLoading();
            }
        }

        private void InitializePowerShell()
        {
            try
            {
                var initialSessionState = InitialSessionState.CreateDefault();
                _runspace = RunspaceFactory.CreateRunspace(initialSessionState);
                _runspace.Open();
                AddLogEntry("PowerShell-Integration initialisiert");
            }
            catch (Exception ex)
            {
                AddLogEntry($"PowerShell-Integration nicht verfügbar: {ex.Message}");
            }
        }

        private void InitializeServers()
        {
            var servers = new[]
            {
                new ServerItem { Name = "DC01", Role = "Domain Controller", StatusIcon = PackIconKind.HelpCircle, StatusColor = Brushes.Gray, StatusText = "Unbekannt" },
                new ServerItem { Name = "DC02", Role = "Domain Controller", StatusIcon = PackIconKind.HelpCircle, StatusColor = Brushes.Gray, StatusText = "Unbekannt" },
                new ServerItem { Name = "DB01", Role = "Database Server", StatusIcon = PackIconKind.HelpCircle, StatusColor = Brushes.Gray, StatusText = "Unbekannt" },
                new ServerItem { Name = "APP01", Role = "Application Server", StatusIcon = PackIconKind.HelpCircle, StatusColor = Brushes.Gray, StatusText = "Unbekannt" },
                new ServerItem { Name = "MAIL", Role = "Mail Server", StatusIcon = PackIconKind.HelpCircle, StatusColor = Brushes.Gray, StatusText = "Unbekannt" },
                new ServerItem { Name = "FILE", Role = "File Server", StatusIcon = PackIconKind.HelpCircle, StatusColor = Brushes.Gray, StatusText = "Unbekannt" },
                new ServerItem { Name = "TS01", Role = "Terminal Server", StatusIcon = PackIconKind.HelpCircle, StatusColor = Brushes.Gray, StatusText = "Unbekannt" }
            };

            foreach (var server in servers)
            {
                _servers.Add(server);
                _serverSelection.Add(new ServerSelectionItem { Name = server.Name, IsSelected = true });
            }
        }

        private async Task PerformPingTestAsync()
        {
            StatusText.Text = "Teste Server-Verbindungen...";
            ProgressBar.Visibility = Visibility.Visible;

            var tasks = _servers.Select(async server =>
            {
                try
                {
                    using (var ping = new Ping())
                    {
                        var reply = await ping.SendPingAsync(server.Name, 3000);
                        
                        Application.Current.Dispatcher.Invoke(() =>
                        {
                            if (reply.Status == IPStatus.Success)
                            {
                                server.StatusIcon = PackIconKind.CheckCircle;
                                server.StatusColor = Brushes.Green;
                                server.StatusText = $"Online ({reply.RoundtripTime}ms)";
                                AddLogEntry($"Server {server.Name}: Online ({reply.RoundtripTime}ms)");
                            }
                            else
                            {
                                server.StatusIcon = PackIconKind.CloseCircle;
                                server.StatusColor = Brushes.Red;
                                server.StatusText = "Offline";
                                AddLogEntry($"Server {server.Name}: Offline ({reply.Status})");
                            }
                        });
                    }
                }
                catch (Exception ex)
                {
                    Application.Current.Dispatcher.Invoke(() =>
                    {
                        server.StatusIcon = PackIconKind.AlertCircle;
                        server.StatusColor = Brushes.Orange;
                        server.StatusText = "Fehler";
                        AddLogEntry($"Server {server.Name}: Fehler - {ex.Message}");
                    });
                }
            });

            await Task.WhenAll(tasks);

            var onlineCount = _servers.Count(s => s.StatusIcon == PackIconKind.CheckCircle);
            StatusText.Text = $"{onlineCount}/{_servers.Count} Server online";
            ProgressBar.Visibility = Visibility.Collapsed;
            
            AddLogEntry($"Ping-Test abgeschlossen: {onlineCount}/{_servers.Count} Server online");
        }

        private async Task LoadDiskSpaceAsync()
        {
            AddLogEntry("Lade Festplatten-Informationen...");
            
            var onlineServers = _servers.Where(s => s.StatusIcon == PackIconKind.CheckCircle).ToList();
            
            foreach (var server in onlineServers)
            {
                try
                {
                    var diskInfo = await GetDiskSpaceAsync(server.Name);
                    server.DiskInfo = new ObservableCollection<DiskItem>(diskInfo);
                    AddLogEntry($"Festplatten-Info für {server.Name} geladen: {diskInfo.Length} Laufwerke");
                }
                catch (Exception ex)
                {
                    AddLogEntry($"Festplatten-Info für {server.Name} nicht verfügbar: {ex.Message}");
                    // Fallback: Dummy-Daten
                    server.DiskInfo = new ObservableCollection<DiskItem>
                    {
                        new DiskItem { Drive = "C:", UsedPercent = 65, DisplayText = "65% (45GB frei)", ProgressColor = Brushes.Orange }
                    };
                }
            }
        }

        private async Task<DiskItem[]> GetDiskSpaceAsync(string serverName)
        {
            return await Task.Run(() =>
            {
                if (_runspace == null) 
                {
                    // Fallback ohne PowerShell
                    return new[]
                    {
                        new DiskItem { Drive = "C:", UsedPercent = 65, DisplayText = "65% (45GB frei)", ProgressColor = Brushes.Orange },
                        new DiskItem { Drive = "D:", UsedPercent = 30, DisplayText = "30% (140GB frei)", ProgressColor = Brushes.Green }
                    };
                }

                try
                {
                    using var ps = PowerShell.Create();
                    ps.Runspace = _runspace;

                    ps.AddScript(@"
                        try {
                            $disks = Get-WmiObject -Class Win32_LogicalDisk -ComputerName '" + serverName + @"' | Where-Object { $_.DriveType -eq 3 -and $_.Size -gt 0 }
                            $results = @()
                            foreach ($disk in $disks) {
                                $usedPercent = [math]::Round((($disk.Size - $disk.FreeSpace) / $disk.Size) * 100, 1)
                                $freeGB = [math]::Round($disk.FreeSpace / 1GB, 1)
                                $results += @{
                                    Drive = $disk.DeviceID
                                    UsedPercent = $usedPercent
                                    FreeGB = $freeGB
                                    TotalGB = [math]::Round($disk.Size / 1GB, 1)
                                }
                            }
                            return $results
                        } catch {
                            return @()
                        }
                    ");

                    var results = ps.Invoke();
                    var diskItems = new List<DiskItem>();

                    foreach (var result in results)
                    {
                        if (result.BaseObject is System.Collections.Hashtable hash)
                        {
                            var usedPercent = Convert.ToDouble(hash["UsedPercent"]);
                            var freeGB = Convert.ToDouble(hash["FreeGB"]);
                            var totalGB = Convert.ToDouble(hash["TotalGB"]);
                            
                            var color = usedPercent > 85 ? Brushes.Red : 
                                       usedPercent > 75 ? Brushes.Orange : Brushes.Green;

                            diskItems.Add(new DiskItem
                            {
                                Drive = hash["Drive"].ToString(),
                                UsedPercent = usedPercent,
                                DisplayText = $"{usedPercent:F1}% ({freeGB:F1}GB frei)",
                                ProgressColor = color
                            });
                        }
                    }

                    return diskItems.ToArray();
                }
                catch
                {
                    // Fallback
                    return new[]
                    {
                        new DiskItem { Drive = "C:", UsedPercent = 65, DisplayText = "65% (45GB frei)", ProgressColor = Brushes.Orange }
                    };
                }
            });
        }

        private void ShowLoading(string message)
        {
            LoadingText.Text = message;
            LoadingOverlay.Visibility = Visibility.Visible;
        }

        private void HideLoading()
        {
            LoadingOverlay.Visibility = Visibility.Collapsed;
        }

        private void Timer_Tick(object sender, EventArgs e)
        {
            TimeText.Text = DateTime.Now.ToString("dd.MM.yyyy HH:mm:ss");
        }

        private void AddLogEntry(string message)
        {
            var timestamp = DateTime.Now.ToString("HH:mm:ss");
            Application.Current.Dispatcher.Invoke(() =>
            {
                LogTextBox.AppendText($"[{timestamp}] {message}\n");
                LogScrollViewer.ScrollToEnd();
            });
        }

        // Event Handlers
        private async void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            await PerformPingTestAsync();
            await LoadDiskSpaceAsync();
        }

        private async void PingButton_Click(object sender, RoutedEventArgs e)
        {
            await PerformPingTestAsync();
        }

        private void UpdatesButton_Click(object sender, RoutedEventArgs e)
        {
            AddLogEntry("Windows Update-Suche - PowerShell-Integration erforderlich");
            MessageBox.Show("Diese Funktion benötigt die vollständige PowerShell-Integration.\n\nVerwenden Sie START-ServerManagement.bat für vollständige Funktionalität.", 
                "Information", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void RestartButton_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show(
                "Möchten Sie alle Server in der korrekten Reihenfolge neu starten?\n\n" +
                "Reihenfolge: DC01 → DC02 → DB01 → APP01 → MAIL → FILE → TS01\n\n" +
                "HINWEIS: Diese Funktion benötigt die vollständige PowerShell-Integration.",
                "Server neu starten", 
                MessageBoxButton.YesNo, 
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                AddLogEntry("Server-Neustart angefordert - PowerShell-Integration erforderlich");
                MessageBox.Show("Verwenden Sie START-ServerManagement.bat für tatsächliche Server-Neustarts.", 
                    "Information", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private async void StatusButton_Click(object sender, RoutedEventArgs e)
        {
            await LoadDiskSpaceAsync();
            AddLogEntry("Detaillierter Server-Status aktualisiert");
        }

        private void SettingsButton_Click(object sender, RoutedEventArgs e)
        {
            AddLogEntry("Einstellungen-Dialog geöffnet");
        }

        private void ShowServerDetails_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is ServerItem server)
            {
                var details = $"SERVER DETAILS: {server.Name}\n" +
                             $"═══════════════════════════\n\n" +
                             $"Rolle: {server.Role}\n" +
                             $"Status: {server.StatusText}\n\n";

                if (server.DiskInfo?.Any() == true)
                {
                    details += "FESTPLATTEN:\n";
                    foreach (var disk in server.DiskInfo)
                    {
                        details += $"  {disk.Drive} {disk.DisplayText}\n";
                    }
                }

                MessageBox.Show(details, $"Details: {server.Name}", MessageBoxButton.OK, MessageBoxImage.Information);
                AddLogEntry($"Details für {server.Name} angezeigt");
            }
        }

        private void RestartSingleServer_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is ServerItem server)
            {
                var result = MessageBox.Show($"Möchten Sie den Server {server.Name} neu starten?", 
                    "Server neu starten", MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    AddLogEntry($"Neustart für {server.Name} angefordert - PowerShell-Integration erforderlich");
                    MessageBox.Show("Verwenden Sie START-ServerManagement.bat für tatsächliche Server-Neustarts.", 
                        "Information", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
        }

        private void ClearLogButton_Click(object sender, RoutedEventArgs e)
        {
            LogTextBox.Clear();
            AddLogEntry("Log gelöscht");
        }

        protected override void OnClosed(EventArgs e)
        {
            _runspace?.Dispose();
            base.OnClosed(e);
        }
    }

    // Data Models
    public class ServerItem : INotifyPropertyChanged
    {
        private PackIconKind _statusIcon;
        private Brush _statusColor;
        private string _statusText;
        private ObservableCollection<DiskItem> _diskInfo;

        public string Name { get; set; }
        public string Role { get; set; }

        public PackIconKind StatusIcon
        {
            get => _statusIcon;
            set
            {
                _statusIcon = value;
                OnPropertyChanged(nameof(StatusIcon));
            }
        }

        public Brush StatusColor
        {
            get => _statusColor;
            set
            {
                _statusColor = value;
                OnPropertyChanged(nameof(StatusColor));
            }
        }

        public string StatusText
        {
            get => _statusText;
            set
            {
                _statusText = value;
                OnPropertyChanged(nameof(StatusText));
            }
        }

        public ObservableCollection<DiskItem> DiskInfo
        {
            get => _diskInfo;
            set
            {
                _diskInfo = value;
                OnPropertyChanged(nameof(DiskInfo));
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;
        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    public class DiskItem : INotifyPropertyChanged
    {
        private double _usedPercent;
        private string _displayText;
        private Brush _progressColor;

        public string Drive { get; set; }

        public double UsedPercent
        {
            get => _usedPercent;
            set
            {
                _usedPercent = value;
                OnPropertyChanged(nameof(UsedPercent));
            }
        }

        public string DisplayText
        {
            get => _displayText;
            set
            {
                _displayText = value;
                OnPropertyChanged(nameof(DisplayText));
            }
        }

        public Brush ProgressColor
        {
            get => _progressColor;
            set
            {
                _progressColor = value;
                OnPropertyChanged(nameof(ProgressColor));
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;
        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    public class ServerSelectionItem : INotifyPropertyChanged
    {
        private bool _isSelected;

        public string Name { get; set; }

        public bool IsSelected
        {
            get => _isSelected;
            set
            {
                _isSelected = value;
                OnPropertyChanged(nameof(IsSelected));
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;
        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
