<Window x:Class="SimpleServerGUI.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="Windows Server Management v2.0" 
        Height="900" Width="1400"
        WindowStartupLocation="CenterScreen"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <materialDesign:DialogHost Identifier="RootDialog">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Header -->
            <materialDesign:ColorZone Grid.Row="0" Mode="PrimaryMid" Padding="16">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0" Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Server" Width="32" Height="32" VerticalAlignment="Center" Foreground="White"/>
                        <TextBlock Text="Windows Server Management v2.0" 
                                   FontSize="20" FontWeight="Bold" 
                                   VerticalAlignment="Center" Margin="12,0" 
                                   Foreground="White"/>
                    </StackPanel>

                    <StackPanel Grid.Column="2" Orientation="Horizontal">
                        <Button x:Name="RefreshButton" 
                                Style="{StaticResource MaterialDesignToolButton}"
                                ToolTip="Aktualisieren"
                                Click="RefreshButton_Click">
                            <materialDesign:PackIcon Kind="Refresh" Foreground="White"/>
                        </Button>
                        <Button x:Name="SettingsButton" 
                                Style="{StaticResource MaterialDesignToolButton}"
                                ToolTip="Einstellungen"
                                Click="SettingsButton_Click">
                            <materialDesign:PackIcon Kind="Settings" Foreground="White"/>
                        </Button>
                    </StackPanel>
                </Grid>
            </materialDesign:ColorZone>

            <!-- Main Content -->
            <Grid Grid.Row="1">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="320"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Sidebar -->
                <materialDesign:Card Grid.Column="0" Margin="8" materialDesign:ShadowAssist.ShadowDepth="Depth2">
                    <ScrollViewer VerticalScrollBarVisibility="Auto">
                        <StackPanel Margin="16">
                            <!-- Action Buttons -->
                            <TextBlock Text="AKTIONEN" FontWeight="Bold" Margin="0,0,0,16" 
                                       Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                            
                            <Button x:Name="PingButton" Style="{StaticResource ActionButton}" 
                                    Click="PingButton_Click">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Wifi" Margin="0,0,8,0"/>
                                    <TextBlock Text="Server anpingen"/>
                                </StackPanel>
                            </Button>

                            <Button x:Name="UpdatesButton" Style="{StaticResource ActionButton}" 
                                    Click="UpdatesButton_Click">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Magnify" Margin="0,0,8,0"/>
                                    <TextBlock Text="Updates suchen"/>
                                </StackPanel>
                            </Button>

                            <Button x:Name="RestartButton" Style="{StaticResource ActionButton}" 
                                    Click="RestartButton_Click">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Restart" Margin="0,0,8,0"/>
                                    <TextBlock Text="Server neu starten"/>
                                </StackPanel>
                            </Button>

                            <Button x:Name="StatusButton" Style="{StaticResource ActionButton}" 
                                    Click="StatusButton_Click">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Information" Margin="0,0,8,0"/>
                                    <TextBlock Text="Detaillierter Status"/>
                                </StackPanel>
                            </Button>

                            <Separator Margin="0,16"/>

                            <!-- Server Selection -->
                            <TextBlock Text="SERVER AUSWAHL" FontWeight="Bold" Margin="0,0,0,16" 
                                       Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                            
                            <CheckBox x:Name="SelectAllCheckBox" Content="Alle Server auswählen" 
                                      Margin="0,0,0,16" IsChecked="True"/>
                            
                            <ItemsControl x:Name="ServerSelectionList">
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <CheckBox Content="{Binding Name}" 
                                                  IsChecked="{Binding IsSelected}"
                                                  Margin="0,4"/>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </StackPanel>
                    </ScrollViewer>
                </materialDesign:Card>

                <!-- Main Content Area -->
                <Grid Grid.Column="1" Margin="0,8,8,8">
                    <TabControl x:Name="MainTabControl" materialDesign:ColorZoneAssist.Mode="PrimaryMid">
                        <!-- Server Overview Tab -->
                        <TabItem>
                            <TabItem.Header>
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="ViewDashboard" Margin="0,0,4,0"/>
                                    <TextBlock Text="Server Übersicht"/>
                                </StackPanel>
                            </TabItem.Header>
                            
                            <ScrollViewer VerticalScrollBarVisibility="Auto">
                                <ItemsControl x:Name="ServerOverviewList" Margin="8">
                                    <ItemsControl.ItemTemplate>
                                        <DataTemplate>
                                            <materialDesign:Card Style="{StaticResource ServerCard}">
                                                <Grid>
                                                    <Grid.RowDefinitions>
                                                        <RowDefinition Height="Auto"/>
                                                        <RowDefinition Height="Auto"/>
                                                        <RowDefinition Height="Auto"/>
                                                    </Grid.RowDefinitions>

                                                    <!-- Server Header -->
                                                    <Grid Grid.Row="0">
                                                        <Grid.ColumnDefinitions>
                                                            <ColumnDefinition Width="Auto"/>
                                                            <ColumnDefinition Width="*"/>
                                                            <ColumnDefinition Width="Auto"/>
                                                        </Grid.ColumnDefinitions>

                                                        <materialDesign:PackIcon Grid.Column="0" 
                                                                                 Kind="{Binding StatusIcon}" 
                                                                                 Foreground="{Binding StatusColor}"
                                                                                 Width="24" Height="24"
                                                                                 VerticalAlignment="Center"/>

                                                        <StackPanel Grid.Column="1" Margin="12,0">
                                                            <TextBlock Text="{Binding Name}" FontSize="18" FontWeight="SemiBold"/>
                                                            <TextBlock Text="{Binding Role}" FontSize="12" 
                                                                       Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                                                        </StackPanel>

                                                        <TextBlock Grid.Column="2" Text="{Binding StatusText}" 
                                                                   FontSize="12" VerticalAlignment="Center"
                                                                   Foreground="{Binding StatusColor}"/>
                                                    </Grid>

                                                    <!-- Disk Space Info -->
                                                    <ItemsControl Grid.Row="1" ItemsSource="{Binding DiskInfo}" Margin="0,8,0,0">
                                                        <ItemsControl.ItemTemplate>
                                                            <DataTemplate>
                                                                <Grid Margin="0,2">
                                                                    <Grid.ColumnDefinitions>
                                                                        <ColumnDefinition Width="Auto"/>
                                                                        <ColumnDefinition Width="*"/>
                                                                        <ColumnDefinition Width="Auto"/>
                                                                    </Grid.ColumnDefinitions>
                                                                    
                                                                    <TextBlock Grid.Column="0" Text="{Binding Drive}" 
                                                                               FontFamily="Consolas" FontSize="11" 
                                                                               Width="30" VerticalAlignment="Center"/>
                                                                    
                                                                    <ProgressBar Grid.Column="1" 
                                                                                 Value="{Binding UsedPercent}" 
                                                                                 Maximum="100" Height="8" 
                                                                                 Margin="4,0"
                                                                                 Foreground="{Binding ProgressColor}"/>
                                                                    
                                                                    <TextBlock Grid.Column="2" 
                                                                               Text="{Binding DisplayText}" 
                                                                               FontSize="10" 
                                                                               VerticalAlignment="Center"/>
                                                                </Grid>
                                                            </DataTemplate>
                                                        </ItemsControl.ItemTemplate>
                                                    </ItemsControl>

                                                    <!-- Action Buttons -->
                                                    <StackPanel Grid.Row="2" Orientation="Horizontal" 
                                                                HorizontalAlignment="Right" Margin="0,8,0,0">
                                                        <Button Style="{StaticResource MaterialDesignIconButton}"
                                                                ToolTip="Details anzeigen"
                                                                Click="ShowServerDetails_Click"
                                                                Tag="{Binding}">
                                                            <materialDesign:PackIcon Kind="Information"/>
                                                        </Button>
                                                        <Button Style="{StaticResource MaterialDesignIconButton}"
                                                                ToolTip="Server neu starten"
                                                                Click="RestartSingleServer_Click"
                                                                Tag="{Binding}">
                                                            <materialDesign:PackIcon Kind="Restart"/>
                                                        </Button>
                                                    </StackPanel>
                                                </Grid>
                                            </materialDesign:Card>
                                        </DataTemplate>
                                    </ItemsControl.ItemTemplate>
                                </ItemsControl>
                            </ScrollViewer>
                        </TabItem>

                        <!-- Activity Log Tab -->
                        <TabItem>
                            <TabItem.Header>
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="FileDocument" Margin="0,0,4,0"/>
                                    <TextBlock Text="Aktivitätsprotokoll"/>
                                </StackPanel>
                            </TabItem.Header>
                            
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="*"/>
                                </Grid.RowDefinitions>

                                <!-- Log Controls -->
                                <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="8">
                                    <Button x:Name="ClearLogButton" Content="Log löschen" 
                                            Style="{StaticResource MaterialDesignOutlinedButton}"
                                            Click="ClearLogButton_Click"/>
                                </StackPanel>

                                <!-- Log Display -->
                                <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" 
                                              x:Name="LogScrollViewer">
                                    <TextBox x:Name="LogTextBox" 
                                             IsReadOnly="True" 
                                             TextWrapping="Wrap"
                                             FontFamily="Consolas"
                                             FontSize="11"
                                             Background="Transparent"
                                             BorderThickness="0"
                                             Margin="8"/>
                                </ScrollViewer>
                            </Grid>
                        </TabItem>
                    </TabControl>
                </Grid>
            </Grid>

            <!-- Status Bar -->
            <materialDesign:ColorZone Grid.Row="2" Mode="PrimaryDark" Padding="8">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock x:Name="StatusText" Grid.Column="0" 
                               Text="Bereit" VerticalAlignment="Center" 
                               Foreground="White"/>

                    <ProgressBar x:Name="ProgressBar" Grid.Column="1" 
                                 Width="200" Height="4" 
                                 Visibility="Collapsed" Margin="8,0"/>

                    <TextBlock x:Name="TimeText" Grid.Column="2" 
                               VerticalAlignment="Center" 
                               Foreground="White"/>
                </Grid>
            </materialDesign:ColorZone>

            <!-- Loading Overlay -->
            <Grid x:Name="LoadingOverlay" Grid.RowSpan="3" 
                  Background="#80000000" Visibility="Collapsed">
                <materialDesign:Card HorizontalAlignment="Center" VerticalAlignment="Center" Padding="32">
                    <StackPanel>
                        <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                                     Value="0" IsIndeterminate="True" 
                                     Width="48" Height="48"/>
                        <TextBlock x:Name="LoadingText" Text="Wird geladen..." 
                                   HorizontalAlignment="Center" Margin="0,16,0,0"/>
                    </StackPanel>
                </materialDesign:Card>
            </Grid>
        </Grid>
    </materialDesign:DialogHost>
</Window>
