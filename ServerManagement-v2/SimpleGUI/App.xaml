<Application x:Class="SimpleServerGUI.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             StartupUri="MainWindow.xaml">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- Material Design -->
                <materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="Blue" SecondaryColor="Cyan" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!-- Custom Styles -->
            <Style x:Key="ActionButton" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
                <Setter Property="Margin" Value="0,0,0,8"/>
                <Setter Property="Padding" Value="16,8"/>
                <Setter Property="materialDesign:ButtonAssist.CornerRadius" Value="4"/>
            </Style>

            <Style x:Key="ServerCard" TargetType="materialDesign:Card">
                <Setter Property="Margin" Value="4"/>
                <Setter Property="Padding" Value="16"/>
            </Style>
        </ResourceDictionary>
    </Application.Resources>
</Application>
