{"format": 1, "restore": {"D:\\Users\\t.gerns\\Documents\\Skripte\\Powershell\\ServerManagement-v2\\SimpleGUI\\SimpleServerGUI.csproj": {}}, "projects": {"D:\\Users\\t.gerns\\Documents\\Skripte\\Powershell\\ServerManagement-v2\\SimpleGUI\\SimpleServerGUI.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Users\\t.gerns\\Documents\\Skripte\\Powershell\\ServerManagement-v2\\SimpleGUI\\SimpleServerGUI.csproj", "projectName": "SimpleServerGUI", "projectPath": "D:\\Users\\t.gerns\\Documents\\Skripte\\Powershell\\ServerManagement-v2\\SimpleGUI\\SimpleServerGUI.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Users\\t.gerns\\Documents\\Skripte\\Powershell\\ServerManagement-v2\\SimpleGUI\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"MaterialDesignColors": {"target": "Package", "version": "[3.1.0, )"}, "MaterialDesignThemes": {"target": "Package", "version": "[5.0.0, )"}, "System.Management.Automation": {"target": "Package", "version": "[7.4.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[8.0.17, 8.0.17]"}, {"name": "Microsoft.AspNetCore.App.Runtime.win-x64", "version": "[8.0.17, 8.0.17]"}, {"name": "Microsoft.NETCore.App.Host.win-x64", "version": "[8.0.17, 8.0.17]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[8.0.17, 8.0.17]"}, {"name": "Microsoft.NETCore.App.Runtime.win-x64", "version": "[8.0.17, 8.0.17]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[8.0.17, 8.0.17]"}, {"name": "Microsoft.WindowsDesktop.App.Runtime.win-x64", "version": "[8.0.17, 8.0.17]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.205/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"win-x64": {"#import": []}}}}}