#Requires -Version 5.1
<#
.SYNOPSIS
    Startet die Windows Server Management Anwendung
.DESCRIPTION
    Kompiliert die Anwendung falls nötig und startet sie
#>

param(
    [switch]$Force  # Erzwingt Neukompilierung
)

$ErrorActionPreference = 'Stop'

Write-Host "🚀 Windows Server Management v2.0" -ForegroundColor Cyan
Write-Host "=================================" -ForegroundColor Cyan
Write-Host ""

# Pfade
$ScriptPath = $PSScriptRoot
$GuiPath = Join-Path $ScriptPath "GUI"
$PublishPath = Join-Path $GuiPath "publish"
$ExePath = Join-Path $PublishPath "ServerManagementApp.exe"

# Prüfe ob .NET 8 verfügbar ist
try {
    $dotnetVersion = dotnet --version
    Write-Host "✓ .NET Version: $dotnetVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ .NET 8 ist nicht installiert!" -ForegroundColor Red
    Write-Host "Bitte installieren Sie .NET 8:" -ForegroundColor Yellow
    Write-Host "  winget install Microsoft.DotNet.SDK.8" -ForegroundColor Gray
    Write-Host ""
    Read-Host "Drücken Sie Enter zum Beenden"
    exit 1
}

# Prüfe ob Anwendung existiert und aktuell ist
$needsBuild = $Force
if (-not (Test-Path $ExePath)) {
    $needsBuild = $true
    Write-Host "⚠️  Anwendung nicht gefunden - wird kompiliert..." -ForegroundColor Yellow
} else {
    $exeTime = (Get-Item $ExePath).LastWriteTime
    $sourceFiles = Get-ChildItem $GuiPath -Recurse -Include "*.cs", "*.xaml", "*.csproj" | Sort-Object LastWriteTime -Descending
    if ($sourceFiles -and $sourceFiles[0].LastWriteTime -gt $exeTime) {
        $needsBuild = $true
        Write-Host "⚠️  Quellcode wurde geändert - wird neu kompiliert..." -ForegroundColor Yellow
    }
}

# Kompiliere falls nötig
if ($needsBuild) {
    Write-Host "🔨 Kompiliere Anwendung..." -ForegroundColor Yellow
    
    Push-Location $GuiPath
    try {
        & .\build.ps1
        if ($LASTEXITCODE -ne 0) {
            throw "Build fehlgeschlagen"
        }
    } finally {
        Pop-Location
    }
    
    Write-Host "✓ Kompilierung erfolgreich" -ForegroundColor Green
    Write-Host ""
}

# Starte Anwendung
if (Test-Path $ExePath) {
    Write-Host "🚀 Starte Windows Server Management..." -ForegroundColor Green
    Write-Host ""
    
    try {
        Start-Process $ExePath
        Write-Host "✓ Anwendung gestartet" -ForegroundColor Green
    } catch {
        Write-Host "❌ Fehler beim Starten: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host ""
        Write-Host "Versuchen Sie die Anwendung manuell zu starten:" -ForegroundColor Yellow
        Write-Host "  $ExePath" -ForegroundColor Gray
        Read-Host "Drücken Sie Enter zum Beenden"
    }
} else {
    Write-Host "❌ Anwendung konnte nicht erstellt werden" -ForegroundColor Red
    Read-Host "Drücken Sie Enter zum Beenden"
}
