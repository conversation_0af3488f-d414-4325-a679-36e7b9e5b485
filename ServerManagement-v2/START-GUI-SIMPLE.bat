@echo off
title Windows Server Management GUI v2.0 - Moderne Version
echo.
echo ========================================
echo  Windows Server Management GUI v2.0
echo  Moderne Version mit Material Design
echo ========================================
echo.

REM Prüfe ob die moderne GUI-Version existiert
if exist "%~dp0SimpleGUI\publish\SimpleServerGUI.exe" (
    echo Starte moderne GUI-Version...
    echo.
    echo FEATURES:
    echo ✓ Material Design Oberflaeche
    echo ✓ Automatischer Ping-Test beim Start
    echo ✓ Live Festplatten-Anzeige aller Partitionen
    echo ✓ Echtzeit Server-Status Updates
    echo ✓ PowerShell-Integration fuer Disk-Space
    echo.
    echo Fuer vollstaendige Funktionalitaet (Updates, Neustarts):
    echo START-ServerManagement.bat
    echo.
    cd /d "%~dp0SimpleGUI\publish"
    SimpleServerGUI.exe
    goto :end
)

echo FEHLER: Einfache GUI-Version nicht gefunden!
echo.
echo Bitte kompilieren Sie die Anwendung zuerst:
echo cd SimpleGUI
echo dotnet publish --configuration Release --output publish --self-contained true --runtime win-x64
echo.
pause

:end
if %errorlevel% neq 0 (
    echo.
    echo Ein Fehler ist aufgetreten. Druecken Sie eine Taste zum Beenden...
    pause >nul
)
