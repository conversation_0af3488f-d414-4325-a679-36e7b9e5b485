# Windows Server Management v2.0

Eine moderne, einfache Windows Server Verwaltungsanwendung mit WPF und Material Design.

## ✨ Features

- **Moderne GUI**: Material Design Interface mit WPF
- **Server-Übersicht**: Alle Server auf einen Blick
- **Ping-Funktionalität**: Teste Server-Konnektivität
- **Server-Neustart**: Einzelne Server oder alle in korrekter Reihenfolge
- **Aktivitätsprotokoll**: Verfolge alle Aktionen
- **Single-File Deployment**: Eine einzige .exe-Datei

## 🚀 Schnellstart

### Voraussetzungen
- Windows 10/11 oder Windows Server 2019+
- .NET 8 Runtime (wird automatisch geprüft)

### Installation von .NET 8 (falls erford<PERSON><PERSON>)
```powershell
winget install Microsoft.DotNet.SDK.8
```

### Anwendung starten
1. Öffnen Sie PowerShell als Administrator
2. Navigieren Sie zum Projektordner
3. Führen Sie aus:
```powershell
.\Start-ServerManagement.ps1
```

## 📋 Standard-Server

Die Anwendung ist vorkonfiguriert für folgende Server:
- **DC01** - Domain Controller (Priorität 1)
- **DC02** - Domain Controller (Priorität 2)  
- **DB01** - Database Server (Priorität 3)
- **APP01** - Application Server (Priorität 4)
- **MAIL** - Mail Server (Priorität 5)
- **FILE** - File Server (Priorität 6)
- **TS01** - Terminal Server (Priorität 7)

## 🔧 Funktionen

### Server-Ping
- Einzelne Server pingen über Button
- Alle Server gleichzeitig pingen
- Anzeige von Antwortzeiten
- Farbkodierte Status-Anzeige

### Server-Neustart
- Einzelne Server neu starten
- Alle Server in korrekter Reihenfolge neu starten
- Automatische Wartezeiten zwischen Neustarts
- Bestätigungsdialoge für Sicherheit

### Aktivitätsprotokoll
- Alle Aktionen werden protokolliert
- Zeitstempel für jede Aktion
- Automatische Begrenzung auf 50 Einträge

## 🛠️ Entwicklung

### Projekt kompilieren
```powershell
dotnet publish ServerManagement.csproj --configuration Release --output publish
```

### Erzwungene Neukompilierung
```powershell
.\Start-ServerManagement.ps1 -Force
```

## 📁 Projektstruktur

```
ServerManagement-Final/
├── ServerManagement.csproj    # Projektdatei
├── App.xaml                   # WPF Application
├── App.xaml.cs               # Application Code-Behind
├── MainWindow.xaml           # Hauptfenster UI
├── MainWindow.xaml.cs        # Hauptfenster Logic
├── Start-ServerManagement.ps1 # Starter-Skript
├── README.md                 # Diese Datei
└── publish/                  # Kompilierte Anwendung
    └── ServerManagement.exe  # Ausführbare Datei
```

## 🎯 Zukünftige Features

- Windows Update Management
- Erweiterte Server-Details
- Konfigurierbare Server-Liste
- Anmeldedaten-Verwaltung
- Automatische Überwachung

## 📝 Hinweise

- Die Anwendung läuft ohne Administratorrechte
- Für Server-Operationen werden Anmeldedaten abgefragt
- Ping-Tests funktionieren ohne zusätzliche Berechtigungen
- Server-Neustarts sind derzeit simuliert (Demo-Modus)

## 🔒 Sicherheit

- Keine Passwörter werden gespeichert
- Alle Server-Operationen erfordern Bestätigung
- Sichere Reihenfolge beim Neustart aller Server

---

**Entwickelt von Tonino Gerns**  
*Windows Server Management v2.0*
