#Requires -Version 5.1
<#
.SYNOPSIS
    Windows Server Management v2.0 - Ein<PERSON><PERSON> Starter
.DESCRIPTION
    Startet die Windows Server Management Anwendung
#>

param(
    [switch]$Force  # Erzwingt Neukompilierung
)

$ErrorActionPreference = 'Stop'

Write-Host "🚀 Windows Server Management v2.0" -ForegroundColor Cyan
Write-Host "=================================" -ForegroundColor Cyan
Write-Host ""

# Pfade
$ScriptPath = $PSScriptRoot
$ExePath = Join-Path $ScriptPath "publish\ServerManagement.exe"

# Prüfe ob .NET 8 verfügbar ist
try {
    $dotnetVersion = dotnet --version
    Write-Host "✓ .NET Version: $dotnetVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ .NET 8 ist nicht installiert!" -ForegroundColor Red
    Write-Host "Bitte installieren Sie .NET 8:" -ForegroundColor Yellow
    Write-Host "  winget install Microsoft.DotNet.SDK.8" -ForegroundColor Gray
    Write-Host ""
    Read-Host "Drücken Sie Enter zum Beenden"
    exit 1
}

# Prüfe ob Anwendung existiert
$needsBuild = $Force
if (-not (Test-Path $ExePath)) {
    $needsBuild = $true
    Write-Host "⚠️  Anwendung nicht gefunden - wird kompiliert..." -ForegroundColor Yellow
} else {
    $exeTime = (Get-Item $ExePath).LastWriteTime
    $sourceFiles = Get-ChildItem $ScriptPath -Include "*.cs", "*.xaml", "*.csproj" -Recurse | Sort-Object LastWriteTime -Descending
    if ($sourceFiles -and $sourceFiles[0].LastWriteTime -gt $exeTime) {
        $needsBuild = $true
        Write-Host "⚠️  Quellcode wurde geändert - wird neu kompiliert..." -ForegroundColor Yellow
    }
}

# Kompiliere falls nötig
if ($needsBuild) {
    Write-Host "🔨 Kompiliere Anwendung..." -ForegroundColor Yellow
    
    try {
        $publishPath = Join-Path $ScriptPath "publish"
        $projectFile = Join-Path $ScriptPath "ServerManagement.csproj"
        
        dotnet publish $projectFile --configuration Release --output $publishPath --verbosity quiet
        
        if ($LASTEXITCODE -ne 0) {
            throw "Build fehlgeschlagen"
        }
        
        Write-Host "✓ Kompilierung erfolgreich" -ForegroundColor Green
        Write-Host ""
    } catch {
        Write-Host "❌ Fehler beim Kompilieren: $($_.Exception.Message)" -ForegroundColor Red
        Read-Host "Drücken Sie Enter zum Beenden"
        exit 1
    }
}

# Starte Anwendung
if (Test-Path $ExePath) {
    Write-Host "🚀 Starte Windows Server Management..." -ForegroundColor Green
    Write-Host ""
    
    try {
        Start-Process $ExePath
        Write-Host "✓ Anwendung gestartet" -ForegroundColor Green
        Write-Host ""
        Write-Host "Die Anwendung sollte sich jetzt öffnen." -ForegroundColor Gray
        Write-Host "Falls nicht, versuchen Sie die Anwendung manuell zu starten:" -ForegroundColor Gray
        Write-Host "  $ExePath" -ForegroundColor Yellow
    } catch {
        Write-Host "❌ Fehler beim Starten: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host ""
        Write-Host "Versuchen Sie die Anwendung manuell zu starten:" -ForegroundColor Yellow
        Write-Host "  $ExePath" -ForegroundColor Gray
    }
} else {
    Write-Host "❌ Anwendung konnte nicht erstellt werden" -ForegroundColor Red
}

Write-Host ""
Write-Host "📚 Weitere Optionen:" -ForegroundColor Cyan
Write-Host "  .\Start-ServerManagement.ps1 -Force    # Erzwingt Neukompilierung" -ForegroundColor Gray
Write-Host ""
Read-Host "Drücken Sie Enter zum Beenden"
