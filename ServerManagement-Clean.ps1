#Requires -Version 5.1
<#
.SYNOPSIS
    Windows Server Management v2.0 - Finale Version
.DESCRIPTION
    Moderne Windows Server Verwaltung mit interaktivem Menu
.AUTHOR
    Tonino Gerns
.VERSION
    2.0.0
#>

# Globale Variablen
$Script:Servers = @(
    @{ Name = "DC01"; Role = "Domain Controller"; Priority = 1; Status = "Unbekannt"; LastChecked = $null }
    @{ Name = "DC02"; Role = "Domain Controller"; Priority = 2; Status = "Unbekannt"; LastChecked = $null }
    @{ Name = "DB01"; Role = "Database Server"; Priority = 3; Status = "Unbekannt"; LastChecked = $null }
    @{ Name = "APP01"; Role = "Application Server"; Priority = 4; Status = "Unbekannt"; LastChecked = $null }
    @{ Name = "MAIL"; Role = "Mail Server"; Priority = 5; Status = "Unbekannt"; LastChecked = $null }
    @{ Name = "FILE"; Role = "File Server"; Priority = 6; Status = "Unbekannt"; LastChecked = $null }
    @{ Name = "TS01"; Role = "Terminal Server"; Priority = 7; Status = "Unbekannt"; LastChecked = $null }
)

$Script:LogEntries = @()

# Funktionen
function Write-Header {
    Clear-Host
    Write-Host "================================================================" -ForegroundColor Cyan
    Write-Host "              Windows Server Management v2.0                   " -ForegroundColor Cyan
    Write-Host "                    Tonino Gerns                               " -ForegroundColor Cyan
    Write-Host "================================================================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Aktuelle Zeit: $(Get-Date -Format 'dd.MM.yyyy HH:mm:ss')" -ForegroundColor Gray
    Write-Host ""
}

function Add-LogEntry {
    param([string]$Message, [string]$Level = "INFO")
    
    $timestamp = Get-Date -Format "HH:mm:ss"
    $entry = "[$timestamp] [$Level] $Message"
    $Script:LogEntries += $entry
    
    # Behalte nur die letzten 50 Eintraege
    if ($Script:LogEntries.Count -gt 50) {
        $Script:LogEntries = $Script:LogEntries[-50..-1]
    }
}

function Show-ServerOverview {
    Write-Host "=== SERVER-UEBERSICHT ===" -ForegroundColor Yellow
    Write-Host ""
    
    $table = @()
    foreach ($server in $Script:Servers) {
        $statusColor = switch ($server.Status) {
            { $_ -like "*Online*" } { "Green" }
            "Offline" { "Red" }
            "Fehler" { "Yellow" }
            default { "Gray" }
        }
        
        $lastChecked = if ($server.LastChecked) { 
            $server.LastChecked.ToString("HH:mm:ss") 
        } else { 
            "Nie" 
        }
        
        $table += [PSCustomObject]@{
            "Nr" = $server.Priority
            "Server" = $server.Name
            "Rolle" = $server.Role
            "Status" = $server.Status
            "Geprueft" = $lastChecked
        }
    }
    
    $table | Format-Table -AutoSize
    
    $onlineCount = ($Script:Servers | Where-Object { $_.Status -like "*Online*" }).Count
    Write-Host "Status: $onlineCount/$($Script:Servers.Count) Server online" -ForegroundColor Cyan
    Write-Host ""
}

function Test-ServerConnectivity {
    param([string]$ServerName)
    
    $server = $Script:Servers | Where-Object { $_.Name -eq $ServerName }
    if (-not $server) { return }
    
    try {
        Write-Host "Pinge $ServerName..." -ForegroundColor Yellow -NoNewline
        
        $ping = Test-Connection -ComputerName $ServerName -Count 1 -Quiet -ErrorAction Stop
        $pingResult = Test-Connection -ComputerName $ServerName -Count 1 -ErrorAction Stop
        
        if ($ping) {
            $responseTime = $pingResult.ResponseTime
            $server.Status = "Online ($responseTime ms)"
            $server.LastChecked = Get-Date
            Write-Host " OK Online ($responseTime ms)" -ForegroundColor Green
            Add-LogEntry "Server $ServerName Online ($responseTime ms)" "SUCCESS"
        } else {
            $server.Status = "Offline"
            $server.LastChecked = Get-Date
            Write-Host " FEHLER Offline" -ForegroundColor Red
            Add-LogEntry "Server $ServerName Offline" "WARNING"
        }
    } catch {
        $server.Status = "Fehler"
        $server.LastChecked = Get-Date
        Write-Host " FEHLER" -ForegroundColor Yellow
        Add-LogEntry "Server $ServerName Fehler - $($_.Exception.Message)" "ERROR"
    }
}

function Test-AllServers {
    Write-Host "=== PING ALLE SERVER ===" -ForegroundColor Yellow
    Write-Host ""
    
    Add-LogEntry "Starte Ping-Test fuer alle Server"
    
    foreach ($server in $Script:Servers | Sort-Object Priority) {
        Test-ServerConnectivity -ServerName $server.Name
        Start-Sleep -Milliseconds 500
    }
    
    Write-Host ""
    $onlineCount = ($Script:Servers | Where-Object { $_.Status -like "*Online*" }).Count
    Write-Host "Ergebnis: $onlineCount/$($Script:Servers.Count) Server sind online" -ForegroundColor Cyan
    Add-LogEntry "Ping-Test abgeschlossen: $onlineCount/$($Script:Servers.Count) Server online"
}

function Restart-SingleServer {
    param([string]$ServerName)
    
    Write-Host "=== SERVER NEUSTART ===" -ForegroundColor Yellow
    Write-Host ""
    
    $confirmation = Read-Host "Moechten Sie den Server $ServerName wirklich neu starten? (j/N)"
    if ($confirmation -ne 'j' -and $confirmation -ne 'J') {
        Write-Host "Neustart abgebrochen." -ForegroundColor Gray
        return
    }
    
    Write-Host "Starte $ServerName neu..." -ForegroundColor Yellow
    Add-LogEntry "Neustart von Server $ServerName wird simuliert..." "INFO"
    
    # Simulation des Neustart-Prozesses
    Write-Host "  -> Sende Neustart-Befehl..." -ForegroundColor Gray
    Start-Sleep -Seconds 2
    
    Write-Host "  -> Warte auf Neustart..." -ForegroundColor Gray
    Start-Sleep -Seconds 3
    
    Write-Host "  -> Teste Konnektivitaet..." -ForegroundColor Gray
    Start-Sleep -Seconds 1
    Test-ServerConnectivity -ServerName $ServerName
    
    Write-Host "OK Neustart von $ServerName abgeschlossen" -ForegroundColor Green
    Add-LogEntry "Server $ServerName Neustart abgeschlossen" "SUCCESS"
}

function Restart-AllServers {
    Write-Host "=== ALLE SERVER NEUSTARTEN ===" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Reihenfolge: DC01 -> DC02 -> DB01 -> APP01 -> MAIL -> FILE -> TS01" -ForegroundColor Cyan
    Write-Host ""
    
    $confirmation = Read-Host "Moechten Sie ALLE Server in der korrekten Reihenfolge neu starten? (j/N)"
    if ($confirmation -ne 'j' -and $confirmation -ne 'J') {
        Write-Host "Neustart abgebrochen." -ForegroundColor Gray
        return
    }
    
    Add-LogEntry "Starte geordneten Neustart aller Server" "INFO"
    
    foreach ($server in $Script:Servers | Sort-Object Priority) {
        Write-Host "Starte $($server.Name) neu..." -ForegroundColor Yellow
        
        # Simulation
        Start-Sleep -Seconds 2
        Test-ServerConnectivity -ServerName $server.Name
        
        if ($server.Priority -lt 7) {
            Write-Host "Warte 30 Sekunden vor dem naechsten Server..." -ForegroundColor Gray
            Start-Sleep -Seconds 3  # Verkuerzt fuer Demo
        }
    }
    
    Write-Host "OK Alle Server wurden neu gestartet" -ForegroundColor Green
    Add-LogEntry "Geordneter Neustart aller Server abgeschlossen" "SUCCESS"
}

function Show-ActivityLog {
    Write-Host "=== AKTIVITAETSPROTOKOLL ===" -ForegroundColor Yellow
    Write-Host ""
    
    if ($Script:LogEntries.Count -eq 0) {
        Write-Host "Keine Aktivitaeten protokolliert." -ForegroundColor Gray
    } else {
        $Script:LogEntries | ForEach-Object {
            if ($_ -like "*ERROR*") {
                Write-Host $_ -ForegroundColor Red
            } elseif ($_ -like "*WARNING*") {
                Write-Host $_ -ForegroundColor Yellow
            } elseif ($_ -like "*SUCCESS*") {
                Write-Host $_ -ForegroundColor Green
            } else {
                Write-Host $_ -ForegroundColor White
            }
        }
    }
    Write-Host ""
}

function Show-Menu {
    Write-Host "=== HAUPTMENU ===" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "1. Server-Uebersicht anzeigen"
    Write-Host "2. Alle Server pingen"
    Write-Host "3. Einzelnen Server pingen"
    Write-Host "4. Einzelnen Server neu starten"
    Write-Host "5. Alle Server neu starten"
    Write-Host "6. Aktivitaetsprotokoll anzeigen"
    Write-Host "7. Einstellungen (in Entwicklung)"
    Write-Host "8. Ueber diese Anwendung"
    Write-Host "0. Beenden"
    Write-Host ""
}

function Show-About {
    Write-Host "=== UEBER DIESE ANWENDUNG ===" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Windows Server Management v2.0" -ForegroundColor Cyan
    Write-Host "Entwickelt von: Tonino Gerns" -ForegroundColor Gray
    Write-Host "Datum: $(Get-Date -Format 'dd.MM.yyyy')" -ForegroundColor Gray
    Write-Host ""
    Write-Host "Features:" -ForegroundColor White
    Write-Host "- Server-Konnektivitaet testen (Ping)"
    Write-Host "- Einzelne Server neu starten"
    Write-Host "- Alle Server in korrekter Reihenfolge neu starten"
    Write-Host "- Aktivitaetsprotokoll"
    Write-Host "- Einfache und uebersichtliche Bedienung"
    Write-Host ""
    Write-Host "Zukuenftige Features:" -ForegroundColor White
    Write-Host "- Windows Update Management"
    Write-Host "- Erweiterte Server-Details"
    Write-Host "- Konfigurierbare Server-Liste"
    Write-Host ""
}

# Hauptprogramm
function Start-ServerManagement {
    Add-LogEntry "Windows Server Management v2.0 gestartet" "INFO"

    do {
        Write-Header
        Show-ServerOverview
        Show-Menu

        $choice = Read-Host "Waehlen Sie eine Option (0-8)"

        switch ($choice) {
            "1" {
                Write-Header
                Show-ServerOverview
                Read-Host "Druecken Sie Enter zum Fortfahren"
            }
            "2" {
                Write-Header
                Test-AllServers
                Read-Host "Druecken Sie Enter zum Fortfahren"
            }
            "3" {
                Write-Header
                Write-Host "Verfuegbare Server:" -ForegroundColor Yellow
                for ($i = 0; $i -lt $Script:Servers.Count; $i++) {
                    Write-Host "$($i + 1). $($Script:Servers[$i].Name) - $($Script:Servers[$i].Role)"
                }
                Write-Host ""
                $serverChoice = Read-Host "Waehlen Sie einen Server (1-$($Script:Servers.Count))"
                if ($serverChoice -match '^\d+$' -and [int]$serverChoice -ge 1 -and [int]$serverChoice -le $Script:Servers.Count) {
                    $selectedServer = $Script:Servers[[int]$serverChoice - 1]
                    Test-ServerConnectivity -ServerName $selectedServer.Name
                } else {
                    Write-Host "Ungueltige Auswahl." -ForegroundColor Red
                }
                Read-Host "Druecken Sie Enter zum Fortfahren"
            }
            "4" {
                Write-Header
                Write-Host "Verfuegbare Server:" -ForegroundColor Yellow
                for ($i = 0; $i -lt $Script:Servers.Count; $i++) {
                    Write-Host "$($i + 1). $($Script:Servers[$i].Name) - $($Script:Servers[$i].Role)"
                }
                Write-Host ""
                $serverChoice = Read-Host "Waehlen Sie einen Server (1-$($Script:Servers.Count))"
                if ($serverChoice -match '^\d+$' -and [int]$serverChoice -ge 1 -and [int]$serverChoice -le $Script:Servers.Count) {
                    $selectedServer = $Script:Servers[[int]$serverChoice - 1]
                    Restart-SingleServer -ServerName $selectedServer.Name
                } else {
                    Write-Host "Ungueltige Auswahl." -ForegroundColor Red
                }
                Read-Host "Druecken Sie Enter zum Fortfahren"
            }
            "5" {
                Write-Header
                Restart-AllServers
                Read-Host "Druecken Sie Enter zum Fortfahren"
            }
            "6" {
                Write-Header
                Show-ActivityLog
                Read-Host "Druecken Sie Enter zum Fortfahren"
            }
            "7" {
                Write-Header
                Write-Host "=== EINSTELLUNGEN ===" -ForegroundColor Yellow
                Write-Host ""
                Write-Host "Diese Funktion wird in einer zukuenftigen Version implementiert." -ForegroundColor Gray
                Write-Host ""
                Write-Host "Geplante Einstellungen:" -ForegroundColor White
                Write-Host "- Server-Liste bearbeiten"
                Write-Host "- Anmeldedaten verwalten"
                Write-Host "- Automatische Ueberwachung"
                Write-Host "- Export-Einstellungen"
                Write-Host ""
                Read-Host "Druecken Sie Enter zum Fortfahren"
            }
            "8" {
                Write-Header
                Show-About
                Read-Host "Druecken Sie Enter zum Fortfahren"
            }
            "0" {
                Write-Host ""
                Write-Host "Auf Wiedersehen!" -ForegroundColor Cyan
                Add-LogEntry "Windows Server Management beendet" "INFO"
                break
            }
            default {
                Write-Host "Ungueltige Auswahl. Bitte waehlen Sie eine Option von 0-8." -ForegroundColor Red
                Start-Sleep -Seconds 2
            }
        }
    } while ($true)
}

# Programm starten
Start-ServerManagement
